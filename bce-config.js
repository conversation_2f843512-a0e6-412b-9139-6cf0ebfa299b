const {defineConfig} = require('@baidu/cba-cli');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const path = require('path');
const MonacoWebpackPlugin = require('monaco-editor-webpack-plugin');

const templateId = '86089d33-aecc-424f-a241-784d4e94d08e'; // 公有云项目ID

const proxyTarget = 'https://console.bce.baidu.com/';
module.exports = defineConfig({
  appName: 'databuilder',
  presets: ['@baidu/cba-preset-console-react'],
  babelOptions: {
    plugins: ['@babel/plugin-proposal-optional-chaining'],
    resolveOptions: (babelConfig) => {
      babelConfig.presets = []; // 让其使用本地 .babelrc 配置
      return babelConfig;
    }
  },
  caching: true,
  rules: ['/api/databuilder/(.*)'],
  root: '.mockrc',
  // proxyTarget: 'https://qasandbox.bcetest.baidu.com',
  proxyTarget,
  i18n: {
    enabled: true,
    independent: false
  },
  templateId, // 功能清单ID
  flags: [], // 功能清单项, 要配置 DB 产品的功能清单
  webpack(config, merge) {
    // 这里merge无法生效，需要手动合并
    config.module.rules[0].oneOf.unshift({
      test: /tailwind\.css$/,
      use: [
        MiniCssExtractPlugin.loader,
        'css-loader',
        {
          loader: 'postcss-loader',
          options: {
            postcssOptions: {
              plugins: ['tailwindcss', 'postcss-preset-env']
            }
          }
        }
      ]
    });
    // 解决jupyter svg文件加载问题
    config.module.rules[0].oneOf.unshift({
      // In .ts and .tsx files (both of which compile to .js), svg files
      // must be loaded as a raw string instead of data URIs.
      test: /\.svg(\?v=\d+\.\d+\.\d+)?$/,
      issuer: /node_modules\/@jupyterlab\/.*\.js$/,
      type: 'asset/source'
    });

    return merge(config, {
      module: {
        rules: [
          {
            // 此规则用于解决 webpack5 中的模块解析问题，针对 @baidu/xicon-react-bigdata 的解析问题
            test: /\.m?js/,
            resolve: {
              fullySpecified: false
            }
          }
        ]
      },
      devServer: {
        proxy: {
          '/api/databuilder/ws': {
            target: proxyTarget,
            changeOrigin: true,
            secure: false,
            ws: true,
            protocolRewrite: 'https',
            upgrade: true,
            pathRewrite: {'^/api/databuilder/ws': '/api/databuilder/ws'}, // 保持路径不变
            onProxyReq(proxyReq) {
              // 某些接口会验证origin
              proxyReq.setHeader('origin', proxyTarget);
            }
          }
        }
      },
      resolve: {
        alias: {
          '@pages': path.resolve('./src/pages'),
          '@components': path.resolve('./src/components'),
          '@hooks': path.resolve('./src/hooks'),
          '@utils': path.resolve('./src/utils'),
          '@api': path.resolve('./src/api'),
          '@assets': path.resolve('./src/assets'),
          '@styles': path.resolve('./src/styles'),
          '@type': path.resolve('./src/type'),
          '@store': path.resolve('./src/store'),
          '@helpers': path.resolve('./src/helpers')
        },
        // 解决prob-image-size包报错问题
        fallback: {
          stream: require.resolve('stream-browserify')
        }
      },
      plugins: [new MonacoWebpackPlugin()]
    });
  }
});
