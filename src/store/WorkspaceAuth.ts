import {PermissionType} from '@api/auth';
import {createSlice} from '@reduxjs/toolkit';

export interface IWorkspaceAuthState {
  catalog: PermissionType;
  compute: PermissionType;
  file: PermissionType;
  job: PermissionType;
  schema: PermissionType;
  table: PermissionType;
  volume: PermissionType;
  workflow: PermissionType;
  metastore: PermissionType;
  workspace: PermissionType;
  metastoreAdmin: 'admin' | undefined; // 是否是元存储管理员
}

const initialState: IWorkspaceAuthState = {
  catalog: undefined,
  compute: undefined,
  file: undefined,
  job: undefined,
  schema: undefined,
  table: undefined,
  volume: undefined,
  workflow: undefined,
  metastore: undefined,
  workspace: undefined,
  metastoreAdmin: undefined // 是的话，返回值为  ["admin"]
};

const workspaceAuthSlice = createSlice({
  name: 'workspaceAuth',
  initialState,
  reducers: {
    updateWorkspaceAuth: (state, action) => {
      [
        'catalog',
        'compute',
        'file',
        'job',
        'schema',
        'table',
        'volume',
        'workflow',
        'metastore',
        'workspace',
        'metastoreAdmin'
      ].forEach((key) => {
        state[key] = action.payload[key]?.[0];
      });
    }
  }
});

export const {updateWorkspaceAuth} = workspaceAuthSlice.actions;

export default workspaceAuthSlice.reducer;
