import {LeftDragTypeEnum, RightDrawerTypeEnum, SelectJobNodeTypeEnum} from '@pages/JobWorkflow/constants';
import {IX6EditNodeData} from '@pages/JobWorkflow/Jobs/components/EditPage/EditContent/X6EditPage/type';
import {createSlice, PayloadAction} from '@reduxjs/toolkit';

// 工作流状态 这里只放置影响页面展示的数据
interface IWorkflowState {
  // 任务名称
  jobName: string;
  // 工作流json
  jsonData: string;
  // 是否编辑
  isEditing: boolean;
  // 表单是否脏 离开页面时需要提示
  formIsDirty: boolean;
  // 是否是json页面
  isJsonPage: boolean;
  // 选中的节点id
  selectedNodeId?: string;
  // 子任务code 算子节点可能分组
  subTaskCode?: string;

  // 选中的节点类型
  selectedNodeType?: SelectJobNodeTypeEnum;

  // 左侧展示类型
  leftDragType: LeftDragTypeEnum;
  // 算子节点父节点id
  operateParentId?: string;
  // 右侧抽屉
  rightDrawer: {
    index: number;
    time: number;
    type: RightDrawerTypeEnum;
  };
  // 编辑的节点数据(右侧抽屉 编辑节点数据)
  editNodeData?: IX6EditNodeData;

  // 刷新时间 用于 刷新结果列表
  refreshTime?: number;
}

const initialState: IWorkflowState = {
  jobName: '',
  jsonData: '',
  isEditing: false,
  formIsDirty: false,
  isJsonPage: false,
  selectedNodeId: undefined,
  rightDrawer: {
    index: 0,
    time: 0,
    type: RightDrawerTypeEnum.INIT
  },
  leftDragType: LeftDragTypeEnum.DEFAULT,
  editNodeData: undefined,
  operateParentId: undefined,
  refreshTime: undefined
};

const workflowSlice = createSlice({
  name: 'workflow',
  initialState,
  reducers: {
    // 设置工作流名称
    setJobName: (state, action: PayloadAction<string>) => {
      state.jobName = action.payload;
    },
    // 设置json 只有切换页面时才调用使用
    setJson: (state, action: PayloadAction<string>) => {
      state.jsonData = action.payload;
      // 切换页面 设置右侧展示 工作流信息
      state.rightDrawer = {
        index: 0,
        time: new Date().getTime(),
        type: RightDrawerTypeEnum.JOB_CONFIG
      };
    },
    // 设置是否是json页面
    setIsJsonPage: (state, action: PayloadAction<boolean>) => {
      state.isJsonPage = action.payload;
    },
    // 设置是否是编辑状态
    setIsEditing: (state, action: PayloadAction<boolean>) => {
      state.isEditing = action.payload;
    },
    // 设置右侧展示类型
    setRightDrawerType: (state, action: PayloadAction<RightDrawerTypeEnum>) => {
      state.rightDrawer = {
        index: 0,
        time: new Date().getTime(),
        type: action.payload
      };
    },
    // 错误提示定位
    setRightDrawerTypeAndIndex: (
      state,
      action: PayloadAction<{type: RightDrawerTypeEnum; index: number}>
    ) => {
      state.rightDrawer = {
        index: action.payload.index,
        time: new Date().getTime(),
        type: action.payload.type
      };
    },
    // 设置选中的节点id
    setSelectedNode: (
      state,
      action: PayloadAction<{id: string; type: SelectJobNodeTypeEnum; subTaskCode?: string}>
    ) => {
      state.selectedNodeId = action.payload.id;
      state.subTaskCode = action.payload.subTaskCode;
      state.selectedNodeType = action.payload.type;
      // 点击 编辑节点  设置左侧拖拽类型 算子
      if (action.payload.type === SelectJobNodeTypeEnum.EDIT_OPERATOR) {
        state.leftDragType = LeftDragTypeEnum.OPERATOR;
        state.selectedNodeType = SelectJobNodeTypeEnum.CLICK;
        state.operateParentId = action.payload.id;
      } else {
        state.leftDragType = LeftDragTypeEnum.DEFAULT;
        state.selectedNodeType = SelectJobNodeTypeEnum.CLICK;
        state.operateParentId = null;
      }
    },

    // 设置编辑的节点数据
    setEditNodeData: (state, action: PayloadAction<IX6EditNodeData>) => {
      state.editNodeData = action.payload;
    },
    // 设置表单是否脏
    setFormIsDirty: (state, action: PayloadAction<boolean>) => {
      state.formIsDirty = action.payload;
    },
    // 设置刷新时间
    setRefreshTime: (state, action: PayloadAction<number>) => {
      state.refreshTime = action.payload;
    },
    // 清空工作流状态
    clearWorkflowState: (state) => {
      return {
        ...initialState,
        // 保留编辑状态
        isEditing: state.isEditing
      };
    }
  }
});

export const {
  setJobName,
  setJson,
  setIsEditing,
  setFormIsDirty,
  setIsJsonPage,
  setRightDrawerType,
  setRightDrawerTypeAndIndex,
  setSelectedNode,
  setEditNodeData,
  clearWorkflowState,
  setRefreshTime
} = workflowSlice.actions;
export default workflowSlice.reducer;
