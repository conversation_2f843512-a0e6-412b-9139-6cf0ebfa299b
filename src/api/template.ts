import {BaseResponseType, request} from '@baidu/bce-react-toolkit';

import {IQueryListParams} from '@type/common';
import {dealQueryParams} from '@utils/utils';

const apiUrl = '/api/databuilder/v1';
// 工作流
export interface ITemplate {
  templateId?: string;
  workspaceId?: string;
  description?: string;
  templateName?: string;
  jobTemplate?: string;
  isPlatformTemplate?: boolean;
  docUrl?: string;
  createdAt?: string;
  createUsername?: string;
  updatedAt?: string;
  updateUser?: string;
  templateThumb?: string;
  code?: string;
}

export interface IQueryTemplateListParams extends IQueryListParams {
  workspaceId?: string;
}

/** 查询列表 */
export function queryTemplateList(
  workspaceId,
  sourceParams?: IQueryTemplateListParams
): BaseResponseType<{
  result: Array<ITemplate>;
  totalCount: number;
}> {
  const params = dealQueryParams(sourceParams);

  return request({
    url: `${apiUrl}/workspaces/${workspaceId}/template/list`,
    method: 'GET',
    params
  });
}

export function getTemplateDetail(params: {
  workspaceId: string;
  templateId: string;
}): BaseResponseType<ITemplate> {
  return request({
    url: `${apiUrl}/workspaces/${params.workspaceId}/template/${params.templateId}`,
    method: 'GET'
  });
}
