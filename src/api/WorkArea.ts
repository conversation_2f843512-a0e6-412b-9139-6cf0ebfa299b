import {BaseResponseType, request} from '@baidu/bce-react-toolkit';
import {AxiosProgressEvent} from 'axios';
import {INotebookContent} from '@jupyterlab/nbformat';
import moment from 'moment';

const urlPrefix = '/api/databuilder/v1';

interface GetWorkspaceFolderListParams {
  workspaceId: string;
  parentId: string;
}

export interface GetWorkspaceFolderListResult {
  id: string;
  name: string;
  path: string;
  parentId: string;
  creator: string;
  createdAt: string;
  type: string;
}

export function getWorkspaceFolderList(
  params: GetWorkspaceFolderListParams
): BaseResponseType<GetWorkspaceFolderListResult[]> {
  return request({
    url: `${urlPrefix}/workspaces/${params.workspaceId}/dirs/list`,
    method: 'GET',
    params: {
      parentId: params.parentId
    }
  });
}

interface GetWorkspaceFileListParams {
  workspaceId: string;
  parentId: string;
  fileName?: string;
  order?: 'asc' | 'desc';
  orderBy?: string;
}

export interface GetWorkspaceFileListResult {
  id: string;
  name: string;
  path: string;
  parentId: string;
  nodeType: 'FILE' | 'FOLDER' | 'TRASH' | 'NOTEBOOK';
  creator: string;
  createdAt: string;
  type: 'HOME' | 'USERS' | 'TRASH' | 'ALL' | 'NORMAL';
}

export function getWorkspaceFileList(
  params: GetWorkspaceFileListParams
): BaseResponseType<GetWorkspaceFileListResult[]> {
  const {workspaceId, parentId, fileName, order, orderBy} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/files/list`,
    method: 'GET',
    params: {
      parentId,
      fileName,
      order,
      orderBy
    }
  });
}

interface CreateWorkspaceFolderParams {
  name: string;
  parentId: string;
  workspaceId: string;
}

export function createWorkspaceFolder(
  params: CreateWorkspaceFolderParams
): BaseResponseType<GetWorkspaceFolderListResult> {
  const {name, parentId, workspaceId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/dirs`,
    method: 'POST',
    data: {
      name,
      parentId
    }
  });
}

export function uploadFile(params: {
  workspaceId: string;
  parentId: string;
  formData: FormData;
  onProgress?: (event: AxiosProgressEvent) => void;
}): BaseResponseType<any> {
  const {workspaceId, parentId, formData, onProgress} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/files`,
    method: 'POST',
    params: {
      parentId
    },
    data: formData,
    onUploadProgress: onProgress
  });
}

interface CopyFileParams {
  workspaceId: string;
  id: string;
  name?: string;
  parentId: string;
}

export function copyFile(params: CopyFileParams) {
  const {workspaceId, id, parentId, name} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/files/${id}`,
    method: 'POST',
    data: {
      parentId,
      name
    }
  });
}

export function copyFolder(params: CopyFileParams) {
  const {workspaceId, id, parentId, name} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/dirs/${id}`,
    method: 'POST',
    data: {
      parentId,
      name
    }
  });
}

interface RenameFileParams {
  workspaceId: string;
  id: string;
  name?: string;
}

export function renameFolder(params: RenameFileParams) {
  const {workspaceId, id, name} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/dirs/${id}`,
    method: 'PATCH',
    data: {
      name
    }
  });
}

export function renameFile(params: RenameFileParams): BaseResponseType<any> {
  const {workspaceId, id, name} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/files/${id}`,
    method: 'PATCH',
    data: {
      name
    }
  });
}

export function moveFile(params: {id: string; parentId: string; workspaceId: string}) {
  const {id, parentId, workspaceId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/files/${id}`,
    method: 'PATCH',
    data: {
      parentId,
      action: 'MOVE'
    }
  });
}

export function moveFolder(params: {id: string; parentId: string; workspaceId: string}) {
  const {id, parentId, workspaceId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/dirs/${id}`,
    method: 'PATCH',
    data: {
      parentId,
      action: 'MOVE'
    }
  });
}

export function deleteFile(params: {id: string; workspaceId: string}) {
  const {id, workspaceId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/files/${id}`,
    method: 'PATCH',
    data: {
      parentId: 'TRASH',
      action: 'DELETE'
    }
  });
}

export function deleteFolder(params: {id: string; workspaceId: string}) {
  const {id, workspaceId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/dirs/${id}`,
    method: 'PATCH',
    data: {
      parentId: 'TRASH',
      action: 'DELETE'
    }
  });
}

export function restoreFile(params: {id: string; parentId: string; workspaceId: string}) {
  const {id, parentId, workspaceId} = params;

  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/files/${id}`,
    method: 'PATCH',
    data: {
      parentId,
      action: 'RESTORE'
    }
  });
}

export function restoreFolder(params: {id: string; parentId: string; workspaceId: string}) {
  const {id, parentId, workspaceId} = params;

  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/dirs/${id}`,
    method: 'PATCH',
    data: {
      parentId,
      action: 'RESTORE'
    }
  });
}

export function permanentDeleteFile(params: {id: string; workspaceId: string}) {
  const {id, workspaceId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/files/${id}`,
    method: 'DELETE'
  });
}

export function permanentDeleteFolder(params: {id: string; workspaceId: string}) {
  const {id, workspaceId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/dirs/${id}`,
    method: 'DELETE'
  });
}

export function getFolderPath(params: {
  id: string;
  workspaceId: string;
}): BaseResponseType<GetWorkspaceFolderListResult[]> {
  const {id, workspaceId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/dirs/${id}/path`,
    method: 'GET'
  });
}

// notebook相关接口
interface CreateNotebookParams {
  workspaceId: string;
  parentId: string;
}
interface CreateNotebookResult {
  id: string;
  name: string;
  path: string;
  parentId: string;
  creator: string;
  createdAt: string;
}

export function createNotebook(params: CreateNotebookParams): BaseResponseType<CreateNotebookResult> {
  const {workspaceId, parentId} = params;
  // return Promise.resolve({
  //   result: {
  //     id: '1',
  //     name: 'test',
  //     path: 'test',
  //     parentId: 'test',
  //     creator: 'test',
  //     createdAt: 'test'
  //   },
  //   success: true,
  //   status: 200
  // });
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/files/notebook`,
    method: 'POST',
    data: {
      parentId,
      name: `Untitled_Notebook_${moment().format('YYYY-MM-DD_HH:mm:ss')}.ipynb`
    }
  });
}

interface GetNotebookContentResult {
  id: string;
  name: string;
  content: INotebookContent;
}

export function getNotebookContent(params: {
  id: string;
  workspaceId: string;
}): BaseResponseType<GetNotebookContentResult> {
  const {id, workspaceId} = params;
  // return Promise.resolve({
  //   result: {
  //     content: nbstring,
  //     id,
  //     name: 'test.ipynb'
  //   },
  //   success: true,
  //   status: 200
  // });
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/files`,
    method: 'GET',
    params: {
      fileId: id
    }
  });
}

export function updateNotebookContent(params: {
  id: string;
  workspaceId: string;
  content: INotebookContent;
}): BaseResponseType<any> {
  const {id, workspaceId, content} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/files`,
    method: 'PUT',
    params: {
      fileId: id
    },
    data: content
  });
}

interface ConnectToComputeParams {
  workspaceId: string;
  computeId: string;
  fileId: string;
  signal?: AbortSignal;
}
interface ConnectToComputeResult {
  id: string; // sessionid
  kernelId: string;
  status: string;
  computeId: string;
}

export function connectToCompute(params: ConnectToComputeParams): BaseResponseType<ConnectToComputeResult> {
  const {workspaceId, computeId, fileId, signal} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/sessions`,
    method: 'POST',
    data: {
      computeId,
      fileId
    },
    signal
  });
}

export function disconnectFromCompute(params: {
  workspaceId: string;
  sessionId: string;
  computeId: string;
}): BaseResponseType<any> {
  const {workspaceId, sessionId, computeId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/sessions`,
    method: 'DELETE',
    data: {
      computeId,
      sessionId
    }
  });
}

interface GetComputeSessionStatusParams {
  workspaceId: string;
  sessionId: string;
  computeId: string;
}
export interface GetComputeSessionStatusResult {
  id: string;
  kernelId: string;
  status: 'UNKNOWN' | 'CONNECTING' | 'CONNECTED' | 'SESSION_ERROR' | 'SERVER_ERROR';
  computeId: string;
}
export function getComputeSessionStatus(
  params: GetComputeSessionStatusParams
): BaseResponseType<GetComputeSessionStatusResult> {
  const {workspaceId, sessionId, computeId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/sessions`,
    method: 'GET',
    params: {
      sessionId,
      computeId
    }
  });
}

interface GetComputeSessionListParams {
  workspaceId: string;
}
interface sessionItem {
  id: string;
  kernel: kernelItem;
  notebook: {
    name: string;
    path: string;
  };
  name: string;
  path: string;
  type: string;
}

export function getComputeSessionList(params: GetComputeSessionListParams): BaseResponseType<sessionItem[]> {
  const {workspaceId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/sessions`,
    method: 'GET'
  });
}

interface GetKernelListParams {
  workspaceId: string;
}
interface GetKernelListResult {
  kernels: kernelItem[];
}
interface kernelItem {
  connections: number;
  execution_state: string;
  id: string;
  last_activity: string;
  name: string;
}
export function getKernelList(params: GetKernelListParams): BaseResponseType<GetKernelListResult[]> {
  const {workspaceId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/kernels`,
    method: 'GET'
  });
}

interface GetKernelSpecsParams {
  workspaceId: string;
}
interface GetKernelSpecsResult {
  default: string;
  kernelspecs: {
    [key: string]: kernelSpec;
  };
}
interface kernelSpec {
  name: string;
  spec: {
    argv: string[];
    display_name: string;
    env: {
      [key: string]: string;
    };
    interrupt_mode: string;
    metadata: {
      [key: string]: string;
    };
    language: string;
    protocol: string;
    requires: string;
    version: string;
  };
}
export function getKernelSpecs(params: GetKernelSpecsParams): BaseResponseType<GetKernelSpecsResult[]> {
  const {workspaceId} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/kernelspecs`,
    method: 'GET'
  });
}

interface UpdateSessionParams {
  workspaceId: string;
  sessionId: string;
  payload: any;
}
export function updateSession(params: UpdateSessionParams): BaseResponseType<any> {
  const {workspaceId, sessionId, payload} = params;
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/sessions/${sessionId}`,
    method: 'PATCH',
    data: {
      id: sessionId,
      ...payload
    }
  });
}
