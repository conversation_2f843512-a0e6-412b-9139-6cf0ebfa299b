/* eslint-disable @typescript-eslint/no-unused-vars */
import {request, BaseResponseType} from '@baidu/bce-react-toolkit';

const urlPrefix = '/api/databuilder/v1';

/** 查询 DBSC 的服务参数 */
export function getServiceParam(): Promise<{
  success: boolean;
  status: number;
  result: Array<{
    roleName: string;
    policyId: string;
    serviceId: string;
    /** 是否激活 */
    isActivated?: boolean;
  }>;
}> {
  // return request({
  //   url: '/api/dbsc/service/param',
  //   method: 'POST'
  // });
  return Promise.resolve({
    success: true,
    status: 200,
    result: [
      {
        roleName: 'BceServiceRole_dsc',
        policyId: 'da5633a62213468dbc810f6b1656459c',
        serviceId: 'dd4c4dd89f3d4dada4cbe7c88fb008b2'
      }
    ]
  });
}

const roleName = 'BceServiceRole_databuilder';
const policyId = 'fe618ec845904bb7b46fd05ae8e73019';
const serviceId = 'd02fe92f8f26484482d3d459482472c3';

/** 判断 IAM 是否已经激活 */
export function queryIamStsRole(): Promise<{
  success: boolean;
  status: number;
  result: {
    id: string;
    name: string;
    type: string;
    grantType: string;
    description: string;
    domain_id: string;
    create_time: string;
  };
}> {
  return request({
    url: '/api/iam/sts/role/query',
    method: 'POST',
    data: {roleName}
  });
  // return new Promise((resolve) => {
  //   setTimeout(() => {
  //     resolve({
  //       success: false,
  //       status: 200,
  //       result: {
  //         id: '6fd5c3431757411c857d13b3d2aff80b',
  //         name: 'BceServiceRole_dsc',
  //         type: 'SERVICE',
  //         grantType: 'ACCOUNT',
  //         description: 'System created role: BceServiceRole_dsc',
  //         domain_id: '3aa23ba0d8734fe5a77a4399401a916b',
  //         create_time: '2023-12-14T08:48:43.430Z'
  //       }
  //     });
  //   }, 5000);
  // });
}

/** 激活产品角色 */
export function activateIamStsRole(params: {accountId: string}): Promise<{
  status: number;
  success: boolean;
}> {
  return request({
    url: '/api/iam/sts/role/activate',
    method: 'POST',
    data: {
      roleName,
      accountId: params.accountId,
      policyId,
      serviceId
    }
  });
}

export function checkWhiteList(): BaseResponseType<{inWhitelist: boolean}> {
  return request({
    url: `${urlPrefix}/account/whitelist/verify`,
    method: 'GET'
  });
}

// doris 节点配置-数量是否有最大限制 白名单
export function checkDorisNodeCountWhiteList(): BaseResponseType<{inWhitelist: boolean}> {
  return request({
    url: `${urlPrefix}/account/whitelist/verify/dorisBeNodeLimit`,
    method: 'GET'
  });
}

// 计算资源-最多 2个ray 2个 doris 的白名单，inWhiteList 即可创建
export function checkComputeLimit(engine): BaseResponseType<{inWhitelist: boolean}> {
  return request({
    url: `${urlPrefix}/account/whitelist/verify/${engine}/computeLimit`,
    method: 'GET'
  });
}

export type PermissionType = 'readWrite' | 'readOnly' | undefined;

export interface GetGlobalPermissionResult {
  workspace: PermissionType[];
  metastore: PermissionType[];
}

export function getGlobalPermission(): BaseResponseType<GetGlobalPermissionResult> {
  return request({
    url: `${urlPrefix}/auth/global/permissions`,
    method: 'GET'
  });
}

export interface GetWorkspacePermissionResult {
  catalog: PermissionType[];
  compute: PermissionType[];
  file: PermissionType[];
  job: PermissionType[];
  schema: PermissionType[];
  table: PermissionType[];
  volume: PermissionType[];
  workflow: PermissionType[];
  workspace: PermissionType[];
}

export function getWorkspacePermission(workspaceId: string): BaseResponseType<GetWorkspacePermissionResult> {
  return request({
    url: `${urlPrefix}/auth/workspaces/${workspaceId}/permissions`,
    method: 'GET'
  });
}

export const setGlobalRegion = (params: {regionId: string}): BaseResponseType<{regionId: string}> => {
  return request({
    url: `/api/region/set`,
    method: 'POST',
    data: params
  });
};

/* 查用户是否有DataBuilderFullControl权限 */
export function checkFullControl(): BaseResponseType<string> {
  return request({
    url: `${urlPrefix}/fullControl`,
    method: 'GET'
  });
}
