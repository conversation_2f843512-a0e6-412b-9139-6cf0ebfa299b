import {BaseResponseType, request} from '@baidu/bce-react-toolkit';

const metastoreUrl = '/api/databuilder/v1/metastore';

export interface IMetastore {
  id: string;
  name: string;
  storageLocation: string;
  comment: string;
  owner: string;
  createdAt: string;
  createdBy: string;
  updatedAt: string;
  updatedBy: string;
  accountId: string;
}

/** 查询元存储 */
export function queryMetastore(silent = false): BaseResponseType<IMetastore> {
  return request({
    url: metastoreUrl,
    method: 'GET',
    silent
  });
}

export interface ICreateMetastoreParams {
  /** 元存储名称 */
  name?: string;
  /** 存储配置 */
  storageLocation?: string;
  /** 管理员 */
  owner: string;
}

/** 创建元存储 */
export function createMetastore(params: ICreateMetastoreParams): BaseResponseType<IMetastore> {
  return request({
    url: metastoreUrl,
    method: 'POST',
    data: params
  });
}

/** 更新元存储 */
export function updateMetastore(params: {owner: string}): BaseResponseType<IMetastore> {
  return request({
    url: metastoreUrl,
    method: 'PATCH',
    data: params
  });
}

/** 删除元存储 */
export function deleteMetastore(): BaseResponseType<null> {
  return request({
    url: metastoreUrl,
    method: 'DELETE'
  });
}
