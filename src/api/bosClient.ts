import {request} from '@baidu/bce-react-toolkit';
import axios, {AxiosInstance} from 'axios';
import {useRegion} from '@baidu/bce-react-toolkit';

// 创建 bosRequest 实例
export const bosRequest: AxiosInstance = axios.create({
  ...request.defaults
});

// 继承 request 的请求拦截器
request.interceptors.request['handlers']?.forEach((handler) => {
  if (handler?.fulfilled || handler?.rejected) {
    bosRequest.interceptors.request.use(handler.fulfilled, handler.rejected);
  }
});

// 继承 request 的响应拦截器
request.interceptors.response['handlers']?.forEach((handler) => {
  if (handler?.fulfilled || handler?.rejected) {
    bosRequest.interceptors.response.use(handler.fulfilled, handler.rejected);
  }
});

// 为 bosRequest 单独添加一个新的响应拦截器
bosRequest.interceptors.response.use(
  (response: any) => {
    if (!response.success || response.success === 'false') {
      return Promise.reject(response.message);
    }

    // bucketlist 接口特殊处理
    return response && (response.result || response.page);
  },
  (error) => {
    return Promise.reject(error);
  }
);

export default class BosClient {
  private _client: any;
  private _context: any;
  private _currentRegion: any;
  private _publicInfo: PublicInfo = {
    region: '',
    userId: ''
  };

  constructor(options: BosSdkConstructor) {
    const {currentRegion} = useRegion();
    const {client, context} = options;
    if (!client) {
      throw new TypeError("Argument for 'client' was not provided.");
    }
    this._context = context;
    this._client = client;
    this._currentRegion = currentRegion?.rawId;
  }

  /**
   * 获取公共参数，region、userid
   */
  private getPublicInfo() {
    if (this._context) {
      let region = this._context.getCurrentRegion();
      if (typeof region === 'object') {
        region = region.id;
      }
      if (this._context.getCurrentRegion())
        this._publicInfo = {
          userId: this._context.getUserId(),
          region: region
        };
    }
  }

  /**
   * 查询白名单
   */
  queryWhilteList(payload = {}): Promise<boolean> {
    this.getPublicInfo();
    return this._client.post('/api/bos/whitelist/query', payload, {
      user_id: this._publicInfo.userId
    });
  }

  /**
   * 查询当前账号可使用地域
   */
  getAvailableRegions(): Promise<{[region: string]: string}> {
    return this._client.post('/api/bos/bucket/location_domain');
  }

  /**
   * 查询当前存储桶名字是否重复
   * @param payload 请求参数
   */
  checkBucketNameDuplicated(payload: {bucketName: string}): Promise<boolean> {
    return this._client.post('/api/bos/bucket/doesExist', payload);
  }

  /**
   * 获取lcc列表
   * @param payload 请求参数
   */
  getLccList(payload: {region: string}): Promise<{
    lccVos: Array<{lccId: string; name: string; capacity: number}>;
  }> {
    return this._client.post('/api/bos/lcc/list', payload);
  }

  /**
   * 创建存储桶
   */
  createBucket(payload: BucketCreateQueryParams): Promise<boolean> {
    return this._client.post('/api/bos/bucket/create', payload);
  }

  /**
   * 获取当前region下的存储桶列表
   */
  getBucketList(): Promise<any> {
    const payload = this._currentRegion ? {locations: [this._currentRegion]} : {};
    return this._client.post('/api/bos/bucket/list', payload);
  }
  /**
   * 获取folder列表
   */
  getFolderList(payload: {bucketName: string; marker: string; prefix: string}): Promise<boolean> {
    return this._client.post('/api/bos/object/folder', payload);
  }
  /**
   * 获取文件列表
   */
  getObjectList(payload: {bucketName: string; marker: string; prefix: string}): Promise<boolean> {
    return this._client.post('/api/bos/object/list', payload);
  }

  /**
   * 创建文件夹
   */
  createFolder(): Promise<boolean> {
    return this._client.post('/api/bos/object/create');
  }
}
