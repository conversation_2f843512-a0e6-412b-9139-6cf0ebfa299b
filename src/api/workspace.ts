import {BaseResponseType, request} from '@baidu/bce-react-toolkit';
import {EWorkspaceStatus, EWorkspaceUserType, EWorkspaceRole} from '@pages/Workspace/constants';
import {OrderType} from '@utils/enums';

const workspaceUrl = '/api/databuilder/v1/workspaces';

// 工作空间 - 列表
export interface IWorkspace {
  id: string;
  /** 工作空间名 */
  name: string;
  /** 状态 */
  status: EWorkspaceStatus;
  /** bos 路径 */
  storageLocation: string;
  /** 元存储 */
  metastore: string;
  /** 描述 */
  desc: string;
  /** 创建者 */
  creator: string;
  /** 创建时间 */
  createdAt: string;
  /** 管理员权限为ADMIN,普通用户为USER,没有权限为UNKNOWN */
  role: EWorkspaceRole | 'UNKNOWN';
}

export interface IQueryWorkspaceListParams {
  pageNo?: number;
  pageSize?: number;
  name?: string;
  order?: OrderType;
  orderBy?: string;
  status?: EWorkspaceStatus;
}

/** 查询工作空间列表 */
export function queryWorkspaceList(params: IQueryWorkspaceListParams): BaseResponseType<{
  items: Array<IWorkspace>;
  total: number;
}> {
  return request({
    url: `${workspaceUrl}/list`,
    method: 'GET',
    params
  });
}

export interface ICreateWorkspaceParams {
  id?: string;
  /** 工作空间名称 */
  name?: string;
  /** 存储配置 */
  storageLocation?: string;
  /** 描述 */
  desc?: string;
}

/** 创建工作空间 */
export function createWorkspace(params: ICreateWorkspaceParams): BaseResponseType<{
  id: number;
}> {
  return request({
    url: workspaceUrl,
    method: 'POST',
    data: params
  });
}

/** 更新工作空间 */
export function updateWorkspace(
  id: string,
  params: ICreateWorkspaceParams
): BaseResponseType<{
  id: number;
}> {
  return request({
    url: `${workspaceUrl}/${id}`,
    method: 'PATCH',
    data: params
  });
}

/** 删除工作空间 */
export function deleteWorkspace(params: IQueryWorkspaceDetailParams): BaseResponseType<{
  id: number;
}> {
  return request({
    url: `${workspaceUrl}/${params.id}`,
    method: 'DELETE'
  });
}

/** 校验工作空间名称是否重复 */
export function checkWorkspaceNameDuplicate(params: {name: string}): BaseResponseType<boolean> {
  return request({
    url: `${workspaceUrl}/check`,
    method: 'GET',
    params
  });
}

// 工作空间 - 详情
export interface IQueryWorkspaceDetailParams {
  id: string;
}

/** 获取工作空间详情 */
export function queryWorkspaceDetail(params: IQueryWorkspaceDetailParams): BaseResponseType<IWorkspace> {
  return request({
    url: `${workspaceUrl}/${params.id}`,
    method: 'GET',
    params
  });
}

// 工作空间 - 权限
export interface IWorkspaceUser {
  entityId: string;
  entityName: string;
  type: EWorkspaceUserType;
  role: EWorkspaceRole;
  createdAt: string;
}

export interface IQueryWorkspaceUserListParams {
  pageNo?: number;
  pageSize?: number;
  entityName?: string;
  order?: OrderType;
  orderBy?: string;
  type?: EWorkspaceStatus;
}

/** 查询工作空间用户列表 */
export function queryWorkspaceUserList(
  workspaceId: string,
  params: IQueryWorkspaceUserListParams
): BaseResponseType<{
  items: Array<IWorkspaceUser>;
  total: number;
}> {
  return request({
    url: `${workspaceUrl}/${workspaceId}/grants`,
    method: 'GET',
    params
  });
}

export interface ICreateWorkspaceUserParams {
  items: Array<{
    type: EWorkspaceUserType;
    entityId: string;
    role: EWorkspaceRole;
    entityName: string;
  }>;
}

/** 添加用户或用户组 */
export function createWorkspaceUser(
  workspaceId: string,
  params: ICreateWorkspaceUserParams
): BaseResponseType<{
  entityId: number;
}> {
  return request({
    url: `${workspaceUrl}/${workspaceId}/grants`,
    method: 'POST',
    data: params
  });
}

/** 更新用户或用户组 */
export function updateWorkspaceUser(
  workspaceId: string,
  params: {
    type: EWorkspaceUserType;
    entityId: string;
    role: EWorkspaceRole;
  }
): BaseResponseType<{
  entityId: number;
}> {
  return request({
    url: `${workspaceUrl}/${workspaceId}/grants`,
    method: 'PATCH',
    data: params
  });
}

export interface IDeleteWorkspaceUserParams {
  type: EWorkspaceUserType;
  entityId: string;
  role: EWorkspaceRole;
}

/** 删除用户或用户组 */
export function deleteWorkspaceUser(
  workspaceId: string,
  params: IDeleteWorkspaceUserParams
): BaseResponseType<{
  entityId: number;
}> {
  return request({
    url: `${workspaceUrl}/${workspaceId}/grants`,
    method: 'DELETE',
    data: params
  });
}

export type IamResponseType<T> = Promise<{
  success: boolean;
  status: number;
  page: {
    result: Array<T>;
  };
}>;

export interface IIamUsers {
  id: string;
  name: string;
}

/** 获取用户列表 */
export function queryIamUserList(): IamResponseType<IIamUsers> {
  return request({
    url: '/api/iam/user/list',
    method: 'POST',
    data: {
      pageNo: 1,
      pageSize: 10000
    }
  });
}

/** 获取用户组列表 */
export function queryIamUserGroupList(): IamResponseType<IIamUsers> {
  return request({
    url: '/api/iam/group/list',
    method: 'POST',
    data: {
      pageNo: 1,
      pageSize: 10000
    }
  });
}
