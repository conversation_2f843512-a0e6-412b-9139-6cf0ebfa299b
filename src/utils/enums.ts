/**
 * 将list转换为map
 */
function convertListToMap<T extends {[key: string]: any}>(list: T[], keyName = 'value'): {[key: string]: T} {
  return list.reduce((result, cur) => Object.assign(result, {[cur[keyName]]: cur}), {});
}

/** 审计规则类型 */
export enum RuleType {
  System = 1,
  Custom
}

/** 审计规则 list */
export const ruleTypeList = [
  {text: '系统自带', value: RuleType.System},
  {text: '用户创建', value: RuleType.Custom}
];

/** 审计规则 map */
export const ruleTypeMap = convertListToMap(ruleTypeList);

/** 风险等级 */
export enum RiskLevel {
  Low = 'low',
  Middle = 'middle',
  High = 'high'
}

/** 风险等级 list */
export const riskLevelList = [
  {label: '高风险', value: RiskLevel.High, backgroundColor: '#f33e3e'},
  {label: '中风险', value: RiskLevel.Middle, backgroundColor: '#ff9326'},
  {label: '低风险', value: RiskLevel.Low, backgroundColor: '#2468f2'}
];

/** 风险等级 map */
export const riskLevelMap = convertListToMap(riskLevelList);

/** 接入状态 */
export enum RegStatus {
  /** 接入成功 */
  Success = 1,
  /** 接入失败 */
  Fail = 2
}

/** 接入状态 list */
export const regStatusList = [
  {
    text: '接入成功',
    value: RegStatus.Success,
    iconClass: 'circle status-success'
  },
  {
    text: '接入失败',
    value: RegStatus.Fail,
    iconClass: 'circle status-error'
  }
];

/** 接入状态 map */
export const regStatusMap = convertListToMap(regStatusList);

/** 审计开关状态 */
export enum AuditOpenState {
  /** 关 */
  Off = 1,
  /** 开 */
  On = 2
}

/** 排序类型 */
export enum OrderType {
  /** 升序 */
  asc = 'asc',
  /** 降序 */
  desc = 'desc'
}

/** 页面操作按钮类型 */
export enum OperateType {
  DETAIL,
  RUN,
  RERUN,
  MORE,
  STOP,
  ADD,
  EDIT,
  PAUSE,
  LOG,
  COPY,
  EXPORT,
  DELETE,
  UPLOAD,
  RECOVER,
  REFRESH,
  RESUME,
  SAVE,
  SWITCH_SCHEDULE,
  SCHEDULE,
  CANCEL
}
