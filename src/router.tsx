import React, {useMemo} from 'react';
import Main, {workspaceFlattenedMenuList, flattenedMenuList, menus, workspaceMenus} from './pages';
import {Suspense} from 'react';
import {createHashRouter, Navigate, RouterProvider} from 'react-router-dom';
import urls from './utils/urls';
import useAuth from '@hooks/useAuth';
import {hasAccess} from '@utils/auth';
import {useAppContext} from '@baidu/bce-react-toolkit';
import {Loading} from 'acud';

const Activation = React.lazy(() => import(/* webpackChunkName: "Activation" */ '@pages/Activation'));

// 主路由
const mainRouter = flattenedMenuList.map((menu) => ({
  path: menu.key,
  element: <Main menus={menus} component={menu.Component} />,
  auth: menu.auth
}));

// 创建一个包装器组件来处理懒加载
const LazyComponent = ({component: Component}: {component?: React.ComponentType<any>}) => {
  return (
    Component && (
      <Suspense
        fallback={
          <div className="db-workspace-wrapper">
            <Loading />
          </div>
        }
      >
        <Component />
      </Suspense>
    )
  );
};

// 工作空间路由 使用二级路由
const workspaceRouter = [
  {
    path: '/workspace',
    id: 'workspace',
    element: <Main menus={workspaceMenus} />,
    children: workspaceFlattenedMenuList.map((menu) => ({
      path: menu.key,
      element: <LazyComponent component={menu.Component} />
    }))
  }
];

const RouterComponent = () => {
  const {appState, appDispatch} = useAppContext();
  const globalMetastoreRead = !!useAuth('global', 'metastore');

  const router = useMemo(() => {
    // 若有auth字段，则根据权限过滤路由
    const authMainRouter = mainRouter.filter((route) =>
      hasAccess({authKey: route.auth, globalMetastoreRead})
    );

    const list = appState.isActivated
      ? [
          ...authMainRouter,
          ...workspaceRouter,
          // TODO: 需要修改
          {
            path: '*',
            element: <Navigate to={urls.manageWorkspace} replace />
          }
        ]
      : [
          {
            path: urls.activation,
            element: <LazyComponent component={Activation} />
          },
          {
            path: '*',
            element: <Navigate to={urls.activation} replace />
          }
        ];
    return createHashRouter(list);
  }, [globalMetastoreRead, appState.isActivated]);

  return <RouterProvider router={router} />;
};

export default RouterComponent;
