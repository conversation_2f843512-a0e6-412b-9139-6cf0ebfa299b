import {OrderType} from '@/utils/enums';
import {EAuth} from '@pages/index';
import React, {ComponentType, FunctionComponent} from 'react';

// 查询列表参数基础参数
export interface IQueryListParams {
  // 分页参数
  pageNo?: number;
  pageSize?: number;
  // 排序参数
  order?: OrderType;
  orderBy?: string;
  // 关键字搜索参数
  keyword?: string;
  keywordType?: string;
  // acud 表格排序参数 处理后会删除
  sorter?: {
    columnKey?: string;
    order?: string;
  };
  // acud 筛选参数 处理后会删除
  filters?: {
    [key: string]: string[] | number[] | boolean | undefined;
  };
}

// 添加menu分组后的enum
export interface MenuItem {
  /** 菜单名称 */
  menuName: string;
  /** 菜单 key */
  key: string;
  /** 路由到该页面时高亮显示的菜单的 key */
  activeMenuKey?: string;
  /** 是否默认展开 */
  isDefaultOpened?: boolean;
  /** 是否为导航菜单 */
  isNavMenu?: boolean;
  /** 是否为顶部导航栏类型  */
  isHeaderNav?: boolean;
  /** 子菜单列表 */
  children?: MenuItem[];
  /** 子节点菜单是否包含顶部导航类菜单 */
  hasHeaderMenu?: boolean;
  /** 顶部组件 */
  Header?: React.FC<any>;
  /** 对应的页面组件 */
  Component?: ComponentType<any>;
  /** 子标题 */
  renderSubtitle?: FunctionComponent<any>;
  /** 是否使用自定义的页面框架，满足右侧页面区域存在特殊场景需求 */
  isPageLayoutCustomized?: boolean;
  /** 是否使用页面容器 */
  isPageWrapperNotRequired?: boolean;
  icon?: React.ReactNode;
  /** 是否是跳转链接 */
  isLink?: boolean;
  /** 页内页外跳转 */
  target?: '_self' | '_blank';
  /** 页内跳转时需重新刷新页面可传此参数 */
  isNeedReload?: boolean;
  /** 是否是menu分组 */
  isMenuGroup?: boolean;
  /** 权限key  */
  auth?: EAuth;
}
