interface Window {
  PRIVATE_STATIC?: {
    disabled?: boolean;
    browserConfig?: {
      title?: string;
      favicon?: string;
    };
    mainPageConfig?: {
      headerInfo?: {
        title?: string;
        logoImg?: string;
        logoImgWidth?: number;
        logoRouteUrl?: string;
        userCenterUrl?: string;
        userCenterOpenType?: string;
        logoutUrl?: string;
      };
    };
    customOtherConfig?: {
      css?: string;
      jsFun?: () => void;
    };
  };
}
