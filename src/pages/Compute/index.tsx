/**
 * 计算资源列表组件
 * 该页面主要用于展示计算资源列表，支持搜索、分页、删除操作
 * 主要逻辑：
 * 1. 获取计算资源列表
 * 2. 渲染列表
 * 3. 搜索功能
 *    - 通过Search组件实现
 * 4. 分页功能
 *    - 通过Pagination组件实现
 * 5. 删除功能
 *    - 通过Modal组件实现
 *    - 通过deleteComputeResource api实现
 * <AUTHOR>
 */
import React, {useState, useCallback, useEffect, useContext, useMemo, act} from 'react';
import {Table, Button, Pagination, toast, Modal, Loading, Tabs, Link, Tooltip} from 'acud';
import {Plus1} from '@baidu/xicon-react-bigdata';
import {getComputeResourceList, ComputeResourceItem, deleteComputeResource, Engine} from '@api/Compute';
import {useNavigate} from 'react-router-dom';
import urls from '@utils/urls';
import {WorkspaceContext} from '@pages/index';
import RefreshButton from '@components/RefreshButton';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import {ComputedMap, ComputeTabPanes, PAY_TYPE, STATUS} from './config';
import useAuth from '@hooks/useAuth';
import useUrlState from '@ahooksjs/use-url-state';
import TextEllipsis from '@components/TextEllipsisTooltip';
import {checkComputeLimit} from '@api/auth';

const cx = classNames.bind(styles);

const ComputeResourceList: React.FC = () => {
  const {TabPane} = Tabs;
  const {workspaceId} = useContext(WorkspaceContext);
  const [urlState, setUrlState] = useUrlState({engine: Engine.Ray});
  const [dataSource, setDataSource] = useState<ComputeResourceItem[]>([]);
  const [pageNo, setPageNo] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(true);
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc' | undefined>(undefined);
  const [sortField, setSortField] = useState<string | undefined>(undefined);
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [activeKey, setActiveKey] = useState<Engine>();
  const [isCreateDisabled, setIsCreateDisabled] = useState<boolean>(false);

  const navigate = useNavigate();
  const computeReadOnly = useAuth('workspace', 'compute') !== 'readWrite';

  const loadComputeList = useCallback(async () => {
    if (!activeKey) {
      return;
    }
    setLoading(true);
    const res = await getComputeResourceList({
      pageNo,
      pageSize,
      workspaceId,
      order: sortOrder,
      orderBy: sortField,
      status: statusFilter?.join(',') || '',
      engine: activeKey
    });
    if (res.success) {
      setTotal(res.result.total);
      setDataSource(res.result.computes);
    }
    setLoading(false);
  }, [pageNo, pageSize, workspaceId, sortOrder, sortField, statusFilter, activeKey]);

  useEffect(() => {
    loadComputeList();
  }, [loadComputeList, activeKey]);

  // 从 url 中初始化 activeKey
  useEffect(() => {
    setActiveKey(urlState.engine || Engine.Ray);
  }, [urlState.engine]);

  const handlePageChange = useCallback((page: number, size: number) => {
    setPageNo(page);
    setPageSize(size);
  }, []);

  // 渲染操作列
  const renderColumnsOptions = useCallback(
    (record) => {
      return (
        <a
          onClick={() => {
            Modal.confirm({
              title: '删除实例',
              content:
                '实例删除后不可恢复，关联任务的运行状态会受影响，关联任务需要重新指定计算实例。确定删除实例？',
              onOk: async () => {
                const res = await deleteComputeResource({
                  workspaceId,
                  computeId: record.computeId || ''
                });
                if (res.success) {
                  toast.success({
                    message: '删除成功',
                    duration: 3
                  });
                  loadComputeList();
                }
              }
            });
          }}
        >
          删除
        </a>
      );
    },
    [loadComputeList, workspaceId]
  );

  // 跳转到数据目录页面
  const goCatalogPage = (catalog) => {
    navigate(`${urls.metaData}?workspaceId=${workspaceId}&catalog=${catalog}`);
  };

  const columns = useMemo(() => {
    return [
      {
        title: '实例 ID',
        dataIndex: 'computeId',
        key: 'computeId',
        width: 150
      },
      {
        title: '实例名称',
        dataIndex: 'name',
        key: 'name',
        width: 300,
        render: (name) => (
          <TextEllipsis tooltip={name} width={300}>
            {name}
          </TextEllipsis>
        )
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        filterMultiple: true,
        filters: STATUS.toArray(),
        filteredValue: statusFilter,
        render: (status: string) => {
          const statusObj = STATUS.fromValue(status);
          return <span className={cx('compute-status', statusObj.className)}>{statusObj.text}</span>;
        }
      },
      {
        title: '节点规格',
        dataIndex: 'clusterType',
        key: 'clusterType',
        width: 120,
        render: (_, record: ComputeResourceItem) => `${record.clusterType}（${record.nodeCount} 个节点）`
      },
      // TODO 这里暂时先写死，后续跟新建-镜像版本级联选择切换接口，同步修改，由后端返回，实际上 key 应该是mirrorVersion
      {
        title: ComputedMap[activeKey]?.engineText,
        dataIndex: 'engine',
        key: 'engine',
        width: 120,
        render: () => (activeKey === Engine.Ray ? 'AI 增强版 1.0' : 'Doris 3.0')
      },
      ...(activeKey === Engine.Doris
        ? [
            {
              title: '数据目录',
              dataIndex: 'catalog',
              key: 'catalog',
              width: 120,
              render: (_, record) =>
                record.status === 'RUNNING' ? (
                  <TextEllipsis tooltip={record.name} width={120}>
                    <Link onClick={() => goCatalogPage(record.name)}>{record.name}</Link>
                  </TextEllipsis>
                ) : (
                  '-'
                )
            }
          ]
        : []),
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        sorter: true,
        width: 150
      },
      {
        title: '付费方式',
        dataIndex: 'chargeType',
        key: 'chargeType',
        width: 120,
        render: (value: string) => {
          return PAY_TYPE.getTextFromValue(value);
        }
      },
      ...(computeReadOnly
        ? []
        : [
            {
              title: '操作',
              key: 'operation',
              width: 150,
              render: (record: ComputeResourceItem) => {
                return renderColumnsOptions(record);
              }
            }
          ])
    ];
  }, [activeKey, computeReadOnly, renderColumnsOptions, statusFilter]);

  const handleTableChange = useCallback((pagination, filters, sorter) => {
    setSortField(sorter.field);

    const orderMap = {
      ascend: 'asc',
      descend: 'desc'
    };
    setSortOrder(orderMap[sorter.order]);

    setStatusFilter(filters.status as string[]);
  }, []);

  const showBlankSpace = useMemo(
    () => !computeReadOnly && dataSource.length === 0 && !statusFilter?.length,
    [computeReadOnly, dataSource.length, statusFilter?.length]
  );

  // 创建实例
  const onCreateClick = useCallback(() => {
    navigate(`${urls.computeCreate}?workspaceId=${workspaceId}&engine=${activeKey}`);
  }, [navigate, workspaceId, activeKey]);

  // 渲染空白状态
  const blankSpace = useMemo(() => {
    const sourceText = ComputedMap[activeKey]?.text;
    const button = (
      <Button
        className={cx('blank-btn')}
        type="primary"
        icon={<Plus1 className="w-4 h-4" />}
        onClick={onCreateClick}
        disabled={isCreateDisabled}
      >
        立即创建
      </Button>
    );
    return (
      <div className={cx('blank-space', 'h-full')}>
        <div className={cx('blank-title')}>创建{sourceText}</div>
        <div className={cx('blank-desc')}>创建{sourceText}用于运行工作流任务</div>
        {isCreateDisabled ? <Tooltip title="创建数量超限">{button}</Tooltip> : button}
        <div className={cx('blank-img')}></div>
      </div>
    );
  }, [activeKey, isCreateDisabled, onCreateClick]);

  const tableList = useMemo(
    () => (
      <>
        <Table
          className={cx('compute-table')}
          columns={columns}
          dataSource={dataSource}
          loading={loading}
          rowKey="computeId"
          pagination={false}
          onChange={handleTableChange}
        />
        <div className={cx('pagination-wrapper', 'flex justify-end mt-4')}>
          <Pagination
            current={pageNo}
            pageSize={pageSize}
            total={total}
            showTotal={(total) => `共 ${total} 条`}
            onChange={(page, pageSize) => handlePageChange(page, pageSize || 10)}
            showSizeChanger
          />
        </div>
      </>
    ),
    [columns, dataSource, handlePageChange, handleTableChange, loading, pageNo, pageSize, total]
  );

  const onTabsChange = useCallback(
    (activeKey) => {
      setActiveKey(activeKey);
      // 重置状态
      setPageNo(1);
      setPageSize(10);
      setStatusFilter([]);
      setSortField(undefined);
      setSortOrder(undefined);
      setUrlState({engine: activeKey});
    },
    [setUrlState]
  );

  // 判断是否在创建数量白名单内
  const getIsInWhitelist = useCallback(async () => {
    const whiteListCheck = await checkComputeLimit(activeKey);
    const isCreate = whiteListCheck?.result?.inWhitelist || false;
    setIsCreateDisabled(!isCreate);
  }, [activeKey]);

  // 调用白名单接口
  useEffect(() => {
    getIsInWhitelist();
  }, [getIsInWhitelist]);

  const createButton = useMemo(() => {
    const button = (
      <Button
        className="ml-[8px]"
        type="primary"
        icon={<Plus1 className="w-4 h-4" />}
        onClick={onCreateClick}
        disabled={isCreateDisabled}
      >
        创建实例
      </Button>
    );
    if (isCreateDisabled) {
      return <Tooltip title="创建数量超限">{button}</Tooltip>;
    }
    return button;
  }, [isCreateDisabled, onCreateClick]);

  return (
    <div className={cx('db-workspace-wrapper', 'compute-wrapper')}>
      <div className={cx('compute-title', 'mb-[16px]')}>计算实例</div>
      <Tabs onChange={onTabsChange} activeKey={activeKey} className={cx('compute-tabs')}>
        {ComputeTabPanes.map((pane) => (
          <TabPane tab={pane.tab} key={pane.key}></TabPane>
        ))}
      </Tabs>
      {loading ? (
        <Loading loading />
      ) : (
        <>
          {showBlankSpace ? (
            blankSpace
          ) : (
            <>
              <div className="flex justify-end mb-4">
                <RefreshButton onClick={loadComputeList} />
                {!computeReadOnly ? createButton : null}
              </div>
              {tableList}
            </>
          )}
        </>
      )}
    </div>
  );
};

export default ComputeResourceList;
