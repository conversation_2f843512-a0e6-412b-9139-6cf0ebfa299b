.compute-wrapper {
  display: flex;
  flex-direction: column;

  .compute-title {
    font-size: 18px;
    color: #151B26;
    line-height: 20px;
    font-weight: bold;
  }

  .compute-tabs{
    flex-shrink: 0;
  }

  .compute-table {
    flex: 1;

    :global {
      .acud-loading-loading-wrapper {
        height: 100%;

        &>div {
          height: 100%;
        }

        .acud-table-empty {
          height: 100%;

          .acud-table-container {
            height: 100%;

            .acud-table-content {
              height: 100%;

              table {
                height: 100%;
              }
            }
          }
        }
      }
    }
  }
}

.compute-status {
  padding: 0 4px;
  display: inline-block;
  border-radius: 10px;
  font-size: 12px;
  line-height: 18px;
  min-width: 52px;
  text-align: center;

  &.deploying {
    background-color: #E6F0FF;
    color: #2468F2;
  }

  &.running {
    background-color: #E7FCEC;
    color: #30BF13;
  }

  &.invalid {
    background-color: rgb(7 12 20 / 6%);;
    color: #151B26;
  }

  &.created-fail {
    background-color: #FEEDEA;
    color: #F33E3E;
  }

}

.pagination-wrapper {
    :global {
        .acud-pagination {
            width: fit-content;
        }
    }
}

.blank-space {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .blank-title {
    font-size: 24px;
    color: #2D2E2E;
    line-height: 32px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 16px;
  }

  .blank-desc {
    font-size: 14px;
    color: #5C5F66;
    text-align: center;
    line-height: 20px;
    margin-bottom: 24px;
  }

  .blank-btn {
    width: 120px;
    height: 32px;
  }

  .blank-img {
    width: 700px;
    height: 280px;
    background-image: url('../../assets/png/compute/blank-page.png');
    background-size: cover;
    background-repeat: no-repeat;
  }
}
