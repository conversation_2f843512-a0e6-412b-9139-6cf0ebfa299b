import {CatalogType} from '@api/metaRequest';
import {RULE} from '@utils/regs';

export const MetaCnNameMap = {
  Catalog: '数据目录',
  Schema: '数据模式',
  Table: '数据表',
  Volume: '数据卷',
  Operator: '算子',
  Model: '模型'
};

// 根据目录类型区分命名正则表达式
export const ruleMapByCatalog = {
  [CatalogType.DATALAKE]: {
    rule: RULE.specialName64,
    text: RULE.specialName64Text
  },
  [CatalogType.DORIS]: {
    rule: RULE.specialNameStartEn64,
    text: RULE.specialNameStartEn64Text
  }
};
