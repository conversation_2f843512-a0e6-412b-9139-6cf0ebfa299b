import {useCallback, useEffect, useMemo, useState} from 'react';
import {But<PERSON>, Drawer, Pagination, Table} from 'acud';
import {useRequest} from 'ahooks';

import DescriptionEdit from '@components/DescriptionEdit';
import InfoPanel from '@components/InfoPanel';
import TextEllipsis from '@components/TextEllipsis';
import IconSvg from '@components/IconSvg';
import {useEventListener} from 'ahooks';

import * as http from '@api/metaRequest';
import {IUrlStateHandler} from '../index';

import MetaActionBar from '../components/MetaActionBar';
import MetaTabs from '../components/MetaTabs';
import ModalRemoveFun from '../components/ModalRemoveFun';
import ModalRename from '../components/ModalRename';
import {MetaCnNameMap} from '../config';

enum PanelEnum {
  OVERVIEW = '1',
  DETAIL = '2'
}

const initialPanes = [
  {tab: '概览', key: PanelEnum.OVERVIEW},
  {tab: '详情', key: PanelEnum.DETAIL}
];

const VersionDetailDrawer = ({
  visible,
  versionInfo,
  setVisible
}: {
  visible: boolean;
  versionInfo: any;
  setVisible: (visible: boolean) => void;
}) => {
  useEventListener('click', () => visible && setVisible(false), {target: document.body});

  const formatParam = (params: http.IOperInputOutput[]) =>
    params && params.length > 0 ? (
      <div>
        {params.map((param, index) => (
          <div className={index === 0 ? '' : 'mt-4'} key={index}>
            <span className="mr-2">字段名：{param.name}</span>
            <span className="mr-2">字段类型：{param.type}</span>
            {param.required && <span className="mr-2">是否必须：{param.required ? '是' : '否'}</span>}
            {param.defaultValue && <span className="mr-2">默认值：{param.defaultValue}</span>}
            {param.comment && <span>字段描述：{param.comment}</span>}
          </div>
        ))}
      </div>
    ) : null;
  const infoList = useMemo(() => {
    return [
      {
        label: '版本号',
        value: versionInfo.name
      },
      {
        label: '版本ID',
        value: versionInfo.id
      },
      {
        label: '版本描述',
        value: versionInfo.comment
      },
      {
        label: '创建人',
        value: versionInfo.createdBy
      },
      {
        label: '创建时间',
        value: versionInfo.createdAt
      },
      {
        label: '最近修改人',
        value: versionInfo.updatedBy
      },
      {
        label: '修改时间',
        value: versionInfo.updatedAt
      },
      {
        label: '代码语言',
        value: versionInfo.language
      },
      {
        label: '算子代码路径',
        value: versionInfo.storageLocation
      },
      {
        label: '算子类型',
        value: versionInfo.category
      },
      {
        label: '运行环境',
        value: versionInfo.runtimeEnv
      },
      {
        label: '输入参数',
        value: formatParam(versionInfo.input)
      },
      {
        label: '输出参数',
        value: formatParam(versionInfo.output)
      },
      {
        label: '运行参数',
        value: formatParam(versionInfo.execParams)
      },
      {
        label: '支持引擎',
        value: versionInfo.engineType
      },
      {
        label: '资源类型',
        value: versionInfo.resourceType
      }
    ];
  }, [versionInfo]);

  return (
    <Drawer
      className="work-meta-version-drawer"
      width={900}
      title="版本详情"
      visible={visible}
      closable
      onClose={() => setVisible(false)}
      mask={false}
    >
      <InfoPanel infoList={infoList} />
    </Drawer>
  );
};

const PanelOperator = (props: IUrlStateHandler) => {
  const {urlState, changeUrlFun, changeUrlFunReplace, handleTreeRefresh, userList, canWrite} = props;
  const {catalog = '', schema = '', node = '', tab = PanelEnum.OVERVIEW} = urlState;

  const dropdownMenu = useMemo(
    () => [
      {key: 'rename', label: `重命名${MetaCnNameMap['Operator']}`, disable: !canWrite},
      {key: 'remove', label: `删除${MetaCnNameMap['Operator']}`, disable: !canWrite}
    ],
    [canWrite]
  );

  // 获取工作空间ID
  // const {workspaceId} = useContext(WorkspaceContext);
  // operator 全名
  const fullName = `${catalog}.${schema}.${node}`;

  // 详情 & 概览
  const [dataInfo, setDataInfo] = useState<http.IOperatorDetailRes>();

  // 分页字段
  const [pagination, setPagination] = useState<{
    pageNo: number;
    pageSize?: number;
  }>({
    pageNo: 1,
    pageSize: 5
  });

  // 抽屉相关
  const [visible, setVisible] = useState(false);
  const [versionInfo, setVersionInfo] = useState<any>({});

  // 详情字段
  const infoList = useMemo(() => {
    const info: any = dataInfo || {};
    return [
      {
        label: `${MetaCnNameMap['Catalog']}名称`,
        value: info.catalogName
      },
      {
        label: `${MetaCnNameMap['Schema']}名称`,
        value: info.schemaName
      },
      {
        label: `${MetaCnNameMap['Operator']}名称`,
        value: info.name
      },
      {
        label: `${MetaCnNameMap['Operator']}别名`,
        value: info.alias
      },
      {
        label: '使用说明',
        value: info.usage
      },
      {
        label: '创建时间',
        value: info.createdAt
      },
      {
        label: '创建人',
        value: userList.find((item) => item.id === info.createdBy)?.name || info.createdBy
      },
      {
        label: '修改时间',
        value: info.updatedAt
      },
      {
        label: '最近修改人',
        value: userList.find((item) => item.id === info.updatedBy)?.name || info.updatedBy
      },
      {
        label: '最新版本 ID',
        value: info.latestVersionId
      },
      {
        label: '最新版本号',
        value: info.latestVersionName
      }
    ];
  }, [dataInfo, userList]);

  //
  const versionColumns = [
    {
      title: '版本号',
      dataIndex: 'name',
      width: 100,
      render: (text: string, record: any) => (
        <Button
          className="version-name-btn"
          type="actiontext"
          onClick={(e) => {
            e.stopPropagation();
            setVisible(true);
            setVersionInfo(record);
          }}
        >
          {text}
        </Button>
      )
    },
    {
      title: 'ID',
      dataIndex: 'id',
      width: 150
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 180
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      width: 180,
      render: (text: string) => {
        return text || '-';
      }
    },
    {
      title: '版本描述',
      dataIndex: 'comment',
      render: (text: string) => {
        return <TextEllipsis text={text || '-'} height={20}></TextEllipsis>;
      }
    }
  ];

  // 获取详情
  const getOperatorDetail = async () => {
    const res = await http.getOperatorDetail(fullName);
    setDataInfo(res.result);
  };

  const {data: versionData, loading} = useRequest(
    async () => {
      if (!catalog || !schema || !node) {
        return;
      }
      return http.getOperatorAllList(fullName, {
        pageNo: pagination.pageNo,
        pageSize: pagination.pageSize
      });
    },
    {
      refreshDeps: [pagination, catalog, schema, node] //  分页 和 排序变化时刷新
    }
  );

  // 初始化
  useEffect(() => {
    catalog && schema && node && getOperatorDetail();
  }, [catalog, schema, node]);

  // tab 切换
  const onTabChange = useCallback(
    (tabkey) => {
      changeUrlFun((preState) => ({...preState, tab: tabkey}));
    },
    [changeUrlFun]
  );

  // 更新描述
  const onChangeDescript = useCallback(
    async (text: string) => {
      await http.patchOperator(fullName, {comment: text});
      getOperatorDetail();
    },
    [dataInfo, setDataInfo]
  );

  // 重命名 & 删除
  const [showRenameModal, setRenameModal] = useState<boolean>(false);
  const renameSuccessFun = useCallback(
    (formData: {name: string}) => {
      changeUrlFunReplace((preState) => ({...preState, node: formData.name}), true);
    },
    [changeUrlFunReplace]
  );
  const removeSuccessFun = useCallback(() => {
    changeUrlFunReplace((preState) => ({...preState, node: null}), true);
  }, [changeUrlFunReplace]);
  const onDropdownClick = useCallback(
    (key) => {
      if (key === 'rename') {
        setRenameModal(true);
      } else if (key === 'remove') {
        ModalRemoveFun({
          fullName,
          name: node,
          title: 'Operator',
          requestFun: http.deleteOperator,
          successFun: removeSuccessFun
        });
      }
    },
    [node, removeSuccessFun]
  );

  // 新建
  const onCreateClick = useCallback(() => {
    console.log('新建 :>> 一期不支持创建 Operator');
  }, []);

  return (
    <div className="work-meta-operator-panel">
      {/* 标题导航操作栏 */}
      <MetaActionBar
        catalog={catalog}
        icon={<IconSvg type="meta-operator" size={20} color="#fff" />}
        title={node as string}
        dropdownMenu={dropdownMenu}
        onDropdownClick={onDropdownClick}
        hiddenCreate
        // createText="新建 Operator"
        // onCreateClick={onCreateClick}
      />
      {/* Tabs */}
      <MetaTabs panesList={initialPanes} tab={tab} onTabChange={onTabChange} />
      {/* Tab-Panel 1 概览 */}
      {tab === PanelEnum.OVERVIEW ? (
        <div>
          <DescriptionEdit
            className="work-meta-operator-panel-edit"
            text={dataInfo?.comment || ''}
            onChangeText={onChangeDescript}
            hasEdit={false}
          />
          <div className="work-meta-operator-panel-usage">
            <div className="title">
              <IconSvg type="metadata-usage-doc" size={20} color="#6c6d70" />
              使用说明
            </div>
            <div className="content">{dataInfo?.usage || '-'}</div>
          </div>
          <div>
            <h2 className="title-head">版本列表</h2>
            <Table
              columns={versionColumns}
              dataSource={versionData?.result?.versions || []}
              loading={loading}
              pagination={false}
            />
            <div className="work-meta-operator-panel-pagination-container">
              <Pagination
                current={pagination.pageNo}
                total={versionData?.result?.totals || 0}
                pageSize={pagination.pageSize}
                onChange={(page, pageSize) => {
                  setPagination({
                    pageNo: page,
                    pageSize: pageSize
                  });
                }}
              />
            </div>
          </div>
        </div>
      ) : null}
      {/* Tab-Panel 2 详情 */}
      {tab === PanelEnum.DETAIL ? <InfoPanel infoList={infoList} title="基本信息" /> : null}
      <VersionDetailDrawer visible={visible} versionInfo={versionInfo} setVisible={setVisible} />
      {/** 重命名 Catalog 弹窗 */}
      <ModalRename
        visible={showRenameModal}
        setVisible={setRenameModal}
        fullName={fullName}
        nowValue={node}
        title="Operator"
        requestFun={http.patchOperator}
        successFun={renameSuccessFun}
      />
    </div>
  );
};
export default PanelOperator;
