import {useCallback, useEffect, useMemo, useState} from 'react';
import DescriptionEdit from '@components/DescriptionEdit';
import InfoPanel from '@components/InfoPanel';

import * as http from '@api/metaRequest';
import {IUrlStateHandler} from '../index';

import MetaActionBar from '../components/MetaActionBar';
import MetaTabs from '../components/MetaTabs';
import ModalCreateName from '../components/ModalCreateName';
import ModalRemoveFun from '../components/ModalRemoveFun';
import ModalRename from '../components/ModalRename';
import CatalogWorkspace from '../components/CatalogWorkspace';
import IconSvg from '@components/IconSvg';
import {OutlinedPlusNew} from 'acud-icon';
import {RULE} from '@utils/regs';
import {useCookieState} from 'ahooks';
import useAuth from '@hooks/useAuth';
import {MetaCnNameMap, ruleMapByCatalog} from '../config';
import {CatalogType} from '@api/metaRequest';

enum PanelEnum {
  DETAIL = '1',
  WORKSPACE = '2'
}

const PanelCatalog = (props: IUrlStateHandler) => {
  const {urlState, changeUrlFun, changeUrlFunReplace, handleTreeRefresh, userList} = props;
  const {catalog = '', tab = PanelEnum.DETAIL} = urlState;
  // 是否是元存储管理员
  const isMetastoreAdmin = useAuth('workspace', 'metastoreAdmin') === 'admin';
  // catalog owner 权限
  const [isOwner, setOwner] = useState<boolean>(false);

  // 详情 & 概览
  const [dataInfo, setDataInfo] = useState<{
    catalog?: http.ICatalogDetailRes;
    metadataSummary?: http.ICatalogSummaryRes;
  }>({});

  // catalog 全名
  const fullName = `${catalog}`;

  // 获取详情
  const [userId] = useCookieState('bce-login-userid');
  const getCatalogDetail = async () => {
    const res = await http.getCatalogDetail(fullName);
    setDataInfo(res.result);
    // 当前登录用户是否是当前 Catalog 的 owner
    setOwner(res.result?.catalog?.owner === userId);
  };

  // 初始化
  useEffect(() => {
    catalog && getCatalogDetail();
  }, [catalog]);

  const catalogType = useMemo(() => dataInfo?.catalog?.type, [dataInfo?.catalog?.type]);

  const dropdownMenu = useMemo(() => {
    // 只有 datalake 类型可以重命名和删除
    const isEdit = catalogType && catalogType === CatalogType.DATALAKE;
    const disabled = !(isMetastoreAdmin || isOwner) || !isEdit;
    return catalog
      ? [
          {
            key: 'rename',
            label: `重命名${MetaCnNameMap['Catalog']}`,
            disabled
          },
          {
            key: 'remove',
            label: `删除${MetaCnNameMap['Catalog']}`,
            disabled
          }
        ]
      : [];
  }, [catalogType, catalog, isMetastoreAdmin, isOwner]);

  /**
   * 1、默认 system 无工作空间
   * 2、只有DATALAKE 类型 并且是【catalog owner】 或【元存储管理员】才可看到到「工作空间tab」
   **/
  const panelsList = useMemo(() => {
    const hasWorkspace = catalogType === CatalogType.DATALAKE && (isOwner || isMetastoreAdmin);
    // 过滤掉工作空间
    return [
      {tab: '详情', key: PanelEnum.DETAIL},
      ...(hasWorkspace ? [{tab: '工作空间', key: PanelEnum.WORKSPACE}] : [])
    ];
  }, [catalogType, isMetastoreAdmin, isOwner]);

  // 详情字段
  const infoList = useMemo(() => {
    const info: any = dataInfo?.catalog || {};
    const isDoris = catalogType === CatalogType.DORIS;
    const catalogShowNameMap = {
      [CatalogType.DATALAKE]: 'Datalake 数据目录',
      [CatalogType.DORIS]: '查询检索实例-Doris 数据目录'
    };
    return [
      {
        label: `${MetaCnNameMap['Catalog']}名称`,
        value: info.name
      },
      {
        label: '创建时间',
        value: info.createdAt
      },
      {
        label: '创建人',
        value: userList.find((item) => item.id === info.createdBy)?.name || info.createdBy
      },
      {
        label: '存储路径',
        value: info.storageLocation
      },
      ...(isDoris
        ? []
        : [
            {
              label: '修改时间',
              value: info.updatedAt
            },
            {
              label: '最近修改人',
              value: userList.find((item) => item.id === info.updatedBy)?.name || info.updatedBy
            }
          ]),
      {
        label: `${MetaCnNameMap['Table']}个数`,
        value: `${dataInfo.metadataSummary?.tableCount || 0}个`
      },
      ...(catalog === CatalogType.SYSTEM
        ? []
        : [
            {
              label: `${MetaCnNameMap['Catalog']}类型`,
              value: catalogShowNameMap[catalogType]
            }
          ]),
      ...(isDoris
        ? [
            {
              label: '查询检索实例名称',
              value: dataInfo.catalog.properties?.computeName
            },
            {
              label: '查询检索实例ID',
              value: dataInfo.catalog.properties?.computeId
            }
          ]
        : [
            {
              label: `${MetaCnNameMap['Volume']}个数`,
              value: `${dataInfo.metadataSummary?.volumeCount || 0}个`
            },
            {
              label: `${MetaCnNameMap['Operator']}个数`,
              value: `${dataInfo.metadataSummary?.operatorCount || 0}个`
            }
          ])
    ];
  }, [
    catalog,
    catalogType,
    dataInfo.catalog,
    dataInfo.metadataSummary?.operatorCount,
    dataInfo.metadataSummary?.tableCount,
    dataInfo.metadataSummary?.volumeCount,
    userList
  ]);

  // tab 切换
  const onTabChange = useCallback(
    (tabkey) => {
      changeUrlFun((preState) => ({...preState, tab: tabkey}));
    },
    [changeUrlFun]
  );

  // 更新描述
  const onChangeDescript = useCallback(
    async (text: string) => {
      await http.patchCatalog(fullName, {comment: text});
      getCatalogDetail();
    },
    [dataInfo, setDataInfo]
  );

  // 重命名 & 删除
  const [showRenameModal, setRenameModal] = useState<boolean>(false);
  const renameSuccessFun = useCallback(
    (formData: {name: string}) => {
      changeUrlFunReplace((preState) => ({...preState, catalog: formData.name}), true);
    },
    [changeUrlFunReplace]
  );
  const removeSuccessFun = useCallback(() => {
    changeUrlFunReplace((preState) => ({...preState, catalog: CatalogType.SYSTEM}), true);
  }, [changeUrlFunReplace]);
  const onDropdownClick = useCallback(
    (key) => {
      if (key === 'rename') {
        setRenameModal(true);
      } else if (key === 'remove') {
        ModalRemoveFun({
          fullName,
          name: catalog,
          title: 'Catalog',
          requestFun: http.deleteCatalog,
          successFun: removeSuccessFun
        });
      }
    },
    [catalog, removeSuccessFun]
  );

  // 新建 Schema 弹窗
  const [showCreateModal, setCreateModal] = useState<boolean>(false);
  const onCreateClick = useCallback(() => {
    setCreateModal(true);
  }, []);
  const createSuccessFun = useCallback(
    (formData: {name: string}) => {
      changeUrlFun((preState) => ({...preState, tab: '1', schema: formData.name}));
      handleTreeRefresh && handleTreeRefresh();
    },
    [changeUrlFun]
  );

  const createSchemaNameRules = useMemo(() => {
    const ruleInfo = ruleMapByCatalog[catalogType];
    return [
      {
        validator: async (_, value) => {
          // 校验特殊字符和长度限制
          if (!ruleInfo.rule.test(value)) {
            return Promise.reject(new Error(ruleInfo.text));
          }
          // 异步校验Volume名称是否重复，复用查询接口 silent模式
          const res = await http.getSchemaDetail(`${catalog}.${value}`, true);
          if (res.success && res.result?.schema?.id) {
            return Promise.reject(new Error(`该${MetaCnNameMap['Schema']}名称已存在，请重新输入`));
          }
          return Promise.resolve();
        }
      }
    ];
  }, [catalog, catalogType]);

  return (
    <div className="work-meta-catalog-panel">
      {/* 标题导航操作栏 */}
      <MetaActionBar
        catalog={catalog}
        icon={
          catalog === CatalogType.SYSTEM ? (
            <IconSvg type="meta-system" size={20} color="#fff" />
          ) : (
            <IconSvg type="meta-catalog" size={20} color="#fff" />
          )
        }
        title={catalog}
        dropdownMenu={dropdownMenu}
        onDropdownClick={onDropdownClick}
        createText={`创建${MetaCnNameMap['Schema']}`}
        createIcon={<OutlinedPlusNew width={16} height={16} />}
        onCreateClick={onCreateClick}
      />
      {/* Tabs */}
      <MetaTabs panesList={panelsList} tab={tab} onTabChange={onTabChange} />
      {/* Tab-Panel 详情 */}
      {tab === PanelEnum.DETAIL ? (
        <>
          <DescriptionEdit
            text={dataInfo?.catalog?.comment || ''}
            onChangeText={onChangeDescript}
            hasEdit={catalog !== CatalogType.SYSTEM && (isMetastoreAdmin || isOwner)}
          />

          <InfoPanel infoList={infoList} title="基本信息" />
        </>
      ) : null}
      {/* Tab-Panel 工作空间 */}
      {tab === PanelEnum.WORKSPACE ? (
        <>
          <div className="catalog-panel-workspace">
            <CatalogWorkspace catalog={dataInfo?.catalog} />
          </div>
        </>
      ) : null}
      {/** 创建 Schema 弹窗 */}
      <ModalCreateName
        visible={showCreateModal}
        setVisible={setCreateModal}
        title="Schema"
        requestFun={http.createSchema}
        requestParams={{catalogName: catalog}}
        successFun={createSuccessFun}
        nameRules={createSchemaNameRules}
        showDescription={catalogType !== CatalogType.DORIS}
      />
      {/** 重命名 Catalog 弹窗 */}
      <ModalRename
        visible={showRenameModal}
        setVisible={setRenameModal}
        fullName={fullName}
        nowValue={catalog}
        title="Catalog"
        requestFun={http.patchCatalog}
        successFun={renameSuccessFun}
        nameRules={[
          {
            validator: async (_, value) => {
              // 校验特殊字符和长度限制
              if (!RULE.specialName64.test(value)) {
                return Promise.reject(new Error(RULE.specialName64Text));
              }
              // 异步校验Volume名称是否重复，复用查询接口 silent模式
              const res = await http.getCatalogDetail(`${value}`, true);
              if (res.success && res.result?.catalog?.id) {
                return Promise.reject(new Error(`该${MetaCnNameMap['Catalog']}名称已存在，请重新输入`));
              }
              return Promise.resolve();
            }
          }
        ]}
      />
    </div>
  );
};
export default PanelCatalog;
