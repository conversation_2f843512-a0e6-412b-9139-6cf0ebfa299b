import React from 'react';
import {useFrameworkContext, useTranslation} from '@baidu/bce-react-toolkit';
import classNames from 'classnames/bind';

import Banner from './components/Banner';
import styles from './index.module.less';

const cx = classNames.bind(styles);

const Activation: React.FC = () => {
  return (
    <div className={cx('activation-container', 'h-full')}>
      <Banner />
    </div>
  );
};

export default Activation;
