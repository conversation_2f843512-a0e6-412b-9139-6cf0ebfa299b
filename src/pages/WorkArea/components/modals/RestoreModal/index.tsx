import React, {forwardRef, useEffect, useImperative<PERSON><PERSON><PERSON>, useState} from 'react';
import {Modal, Form, TreeSelect} from 'acud';
import {DataNode} from 'acud/es/tree';
import type {GetWorkspaceFileListResult} from '@api/WorkArea';
import {getWorkspaceFolderList, restoreFile, restoreFolder} from '@api/WorkArea';
import {useRequest} from 'ahooks';
import _ from 'lodash';

interface RestoreModalProps {
  workspaceId: string;
  onSuccess?: (record: GetWorkspaceFileListResult, targetDir: string) => void;
}

interface TreeNode extends DataNode {
  title: string;
  value: string;
}

export interface RestoreModalRef {
  open: (record: GetWorkspaceFileListResult) => void;
  close: () => void;
}

// 更新树数据的辅助函数
const updateTreeData = (list: TreeNode[], key: React.Key, children: TreeNode[]): TreeNode[] => {
  return list.map((node) => {
    if (node.key === key) {
      return {
        ...node,
        children
      };
    }
    if (node.children) {
      return {
        ...node,
        children: updateTreeData(node.children as TreeNode[], key, children)
      };
    }
    return node;
  });
};

async function loadTreedData(workspaceId: string, key?: React.Key) {
  const res = await getWorkspaceFolderList({
    parentId: key as string,
    workspaceId
  });
  return _.filter(res.result, (item) => item.type !== 'TRASH').map((item) => ({
    title: item.name,
    key: item.id,
    value: item.id,
    isLeaf: false,
    type: item.type
  }));
}

const RestoreModal = forwardRef<RestoreModalRef, RestoreModalProps>((props, ref) => {
  const [visible, setVisible] = useState(false);
  const [record, setRecord] = useState<GetWorkspaceFileListResult | null>(null);
  const [form] = Form.useForm();
  const [treeData, setTreeData] = useState<TreeNode[]>([]);
  const [loadedKeys, setLoadedKeys] = useState<React.Key[]>([]);
  const [loading, setLoading] = useState(false);

  const {run: move} = useRequest(
    ({id, parentId, workspaceId}) => {
      const api = {
        FILE: restoreFile,
        NOTEBOOK: restoreFile,
        FOLDER: restoreFolder
      }[record?.nodeType || 'FILE'];
      return api({id, parentId, workspaceId});
    },
    {
      manual: true,
      onSuccess: () => {
        const {targetDir} = form.getFieldsValue();
        props.onSuccess?.(record, targetDir);
        handleClose();
      },
      onError: (error) => {
        console.error('移动失败:', error);
      }
    }
  );

  useImperativeHandle(ref, () => ({
    open: (record: GetWorkspaceFileListResult) => {
      setVisible(true);
      setRecord(record);
      setLoadedKeys([]);

      form.setFieldsValue({name: record?.name});
      loadTreedData(props.workspaceId, 'USERS').then((res) => {
        const homeTree = _.filter(res, (item) => item.type === 'HOME');
        setTreeData(res);
        form.setFieldsValue({targetDir: homeTree?.[0]?.key});
      });
    },
    close: handleClose
  }));

  // 异步加载子节点
  const onLoadData = async (node) => {
    const {key} = node;

    // 如果已经加载过，不再重复加载
    if (loadedKeys.includes(key) || node.isLeaf) {
      return;
    }

    setLoading(true);
    try {
      const children = await loadTreedData(props.workspaceId, key);
      setTreeData((origin) => updateTreeData(origin, key, children));
      setLoadedKeys((prevKeys) => [...prevKeys, key]);
    } catch (error) {
      console.error('加载子目录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleConfirm = async () => {
    await form.validateFields();
    const {targetDir} = form.getFieldsValue();
    await move({
      id: record?.id || '',
      parentId: targetDir,
      workspaceId: props.workspaceId
    });
  };

  const handleClose = () => {
    setVisible(false);
    form.resetFields();
  };

  return (
    <Modal title="恢复" visible={visible} onOk={handleConfirm} onCancel={handleClose}>
      <Form form={form} layout="vertical" inputMaxWidth="100%">
        <Form.Item name="targetDir" label="目标目录" rules={[{required: true, message: '请选择目标目录'}]}>
          <TreeSelect
            style={{width: '100%'}}
            dropdownStyle={{maxHeight: 400, overflow: 'auto'}}
            placeholder="请选择目标目录"
            loadData={onLoadData}
            treeData={treeData}
            loading={loading}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
});

RestoreModal.displayName = 'RestoreModal';

export default RestoreModal;
