import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {Form, Modal, Input} from 'acud';
import type {GetWorkspaceFileListResult} from '@api/WorkArea';
import {copyFile, copyFolder} from '@api/WorkArea';
import {useRequest} from 'ahooks';
import {FILE_NAME_ERROR_MESSAGE, validatePath} from '@utils/utils';
import {FOLDER_NAME_REGEX, FOLDER_LIMIT_LENGTH, FOLDER_NAME_ERROR_MESSAGE} from '../../../config';

interface CopyModalProps {
  workspaceId: string;
  onSuccess?: (record: GetWorkspaceFileListResult) => void;
}

export interface CopyModalRef {
  open: (record: GetWorkspaceFileListResult) => void;
  close: () => void;
}

const CopyModal = forwardRef<CopyModalRef, CopyModalProps>((props, ref) => {
  const [visible, setVisible] = useState(false);
  const [record, setRecord] = useState<GetWorkspaceFileListResult | null>(null);

  const [form] = Form.useForm();

  useImperativeHandle(ref, () => ({
    open: (record: GetWorkspaceFileListResult) => {
      setVisible(true);
      setRecord(record);
      form.setFieldsValue({name: `${record.name}-复制`});
    },
    close: handleClose
  }));

  const {run: copy, loading} = useRequest(
    ({name, workspaceId, id, parentId}) => {
      const api = {
        FILE: copyFile,
        NOTEBOOK: copyFile,
        FOLDER: copyFolder
      }[record?.nodeType || 'FILE'];
      return api({name, workspaceId, id, parentId});
    },
    {
      manual: true,
      onSuccess: () => {
        props.onSuccess?.(record!);
        handleClose();
      },
      onError: (error) => {
        console.error('复制失败:', error);
      }
    }
  );

  const handleConfirm = async () => {
    await form.validateFields();
    const name = form.getFieldValue('name');
    await copy({
      name,
      workspaceId: props.workspaceId,
      id: record?.id || '',
      parentId: record?.parentId || ''
    });
  };

  const handleClose = () => {
    setVisible(false);
    form.resetFields();
  };

  const nameRules = [
    {required: true, message: '请输入名称'},
    {
      validator: (_, value) => {
        if (!FOLDER_NAME_REGEX.test(value)) {
          return Promise.reject(new Error(FOLDER_NAME_ERROR_MESSAGE));
        }
        return Promise.resolve();
      }
    }
  ];

  const fileNameRules = [
    {
      validator: (_, value) => {
        if (!validatePath(value)) {
          return Promise.reject(new Error(FILE_NAME_ERROR_MESSAGE));
        }
        return Promise.resolve();
      }
    }
  ];

  const type = record?.nodeType === 'FOLDER' ? 'FOLDER' : 'FILE';

  return (
    <Modal
      title="复制"
      visible={visible}
      onOk={handleConfirm}
      onCancel={handleClose}
      confirmLoading={loading}
    >
      <Form form={form} layout="vertical" inputMaxWidth="100%">
        {type === 'FOLDER' ? (
          <Form.Item name="name" label="新名称" rules={nameRules} extra={FOLDER_NAME_ERROR_MESSAGE}>
            <Input limitLength={FOLDER_LIMIT_LENGTH} forbidIfLimit placeholder="请输入名称" />
          </Form.Item>
        ) : (
          <Form.Item
            required
            name="name"
            label="新名称"
            rules={fileNameRules}
            extra={FILE_NAME_ERROR_MESSAGE}
          >
            <Input placeholder="请输入名称" />
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
});

CopyModal.displayName = 'CopyModal';

export default CopyModal;
