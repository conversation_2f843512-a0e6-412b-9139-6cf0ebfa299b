{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["hello world\n", "0\n", "1\n", "2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["output to stderr\n"]}, {"name": "stdout", "output_type": "stream", "text": ["some more stdout text\n"]}], "source": ["import sys\n", "sys.stdout.write('hello world\\n')\n", "sys.stdout.flush()\n", "for i in range(3):\n", "    sys.stdout.write('%s\\n' % i)\n", "    sys.stdout.flush()\n", "sys.stderr.write('output to stderr\\n')\n", "sys.stderr.flush()\n", "sys.stdout.write('some more stdout text\\n')\n", "sys.stdout.flush()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Markdown Cell\n", "\n", "$ e^{ \\pm i\\theta } = \\cos \\theta \\pm i\\sin \\theta + \\beta $\n", "\n", "*It* **really** is!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["test\n"]}], "source": ["print('test')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["The mass-energy equivalence is described by the famous equation\n", " \n", "$$E=mc^2$$\n", " \n", "discovered in 1905 by <PERSON>. \n", "In natural units ($c$ = 1), the formula expresses the identity\n", " \n", "\\begin{equation}\n", "E=m\n", "\\end{equation}"], "text/plain": ["<IPython.core.display.Latex object>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from IPython.display import Latex\n", "Latex('''The mass-energy equivalence is described by the famous equation\n", " \n", "$$E=mc^2$$\n", " \n", "discovered in 1905 by <PERSON>. \n", "In natural units ($c$ = 1), the formula expresses the identity\n", " \n", "\\\\begin{equation}\n", "E=m\n", "\\\\end{equation}''')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6b433509d21d4de0860d6a65c9fc3031", "version_major": 2, "version_minor": 0}, "text/plain": ["IntSlider(value=0)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b769f28cfced44fe971b97ae2876e865", "version_major": 2, "version_minor": 0}, "text/plain": ["IntText(value=0)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display\n", "import ipywidgets as w\n", "# s = w.<PERSON><PERSON><PERSON><PERSON><PERSON>(0, 10)£\n", "# s\n", "a = w.<PERSON><PERSON>()\n", "b = w.IntText()\n", "w.jslink((a, 'value'), (b, 'value'))\n", "display(a, b)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"bingroup": "x", "histfunc": "sum", "hovertemplate": "sex=Female<br>total_bill=%{x}<br>sum of tip=%{y}<extra></extra>", "legendgroup": "Female", "marker": {"color": "#636efa", "pattern": {"shape": ""}}, "name": "Female", "orientation": "v", "showlegend": true, "type": "histogram", "x": {"bdata": "PQrXo3D9MEDXo3A9Cpc4QOF6FK5HoUFAKVyPwvWoLUApXI/C9agkQLgehetR+DBACtejcD1KNEAK16NwPYovQGZmZmZmpjNAH4XrUbgeLkBxPQrXo7A0QK5H4XoU7jBAFK5H4XqUJEBI4XoUrmdBQClcj8L1aDpAMzMzMzNzMECPwvUoXI8IQFK4HoXrETFAXI/C9SjcOkBI4XoUrkc5QPYoXI/CdS1ApHA9CtcjJEAK16NwPWpBQAAAAAAAABdAUrgehetRMEAAAAAAAMA2QDMzMzMzsyZAw/UoXI/CLkBmZmZmZiZGQOxRuB6FazZA7FG4HoXrNEAfhetRuJ4sQAAAAAAAAB1A9ihcj8K1OUCPwvUoXE8xQM3MzMzMTCVAXI/C9SjcKEAUrkfhehQ4QNejcD0K1ypA9ihcj8L1KEDNzMzMzMw9QArXo3A9Ci1Aw/UoXI/CJkCF61G4HkU0QNejcD0KVyZAhetRuB6FKEDD9Shcj0IyQIXrUbgeBSFAKVyPwvWoJEDNzMzMzEwsQFK4HoXrUSpAuB6F61F4MUDNzMzMzAw7QK5H4XoUbjBAMzMzMzOzIECkcD0K16MyQD0K16NwvSdAmpmZmZnZPUAAAAAAAAA5QEjhehSuxypA9ihcj8I1MEDD9Shcj4IxQK5H4XoULiVAw/UoXI9CJUAzMzMzMzMjQGZmZmZm5jRAZmZmZmYmMkCPwvUoXM8zQK5H4XoUjkVAAAAAAAAAKkB7FK5H4XopQAAAAAAAACpAZmZmZmZmMEC4HoXrUXgwQIXrUbgehSlACtejcD2KKkDsUbgehSs8QM3MzMzMzClApHA9CtcjPkDXo3A9CtcqQPYoXI/C9S9AhetRuB5FMECuR+F6FC4kQB+F61G4HjZACtejcD3qQUCuR+F6FC47QEjhehSuxzJA", "dtype": "f8"}, "xaxis": "x", "y": {"bdata": "KVyPwvUo8D/hehSuR+EMQAAAAAAAABRAKVyPwvUoCEC4HoXrUbj6PwAAAAAAAAxAAAAAAAAABkDXo3A9CtcBQAAAAAAAAAhAAAAAAAAACECamZmZmZkDQI/C9ShcjwhAzczMzMzMBEDNzMzMzMwUQAAAAAAAAPg/w/UoXI/CA0AAAAAAAADwPwAAAAAAAAhAH4XrUbgeCUAAAAAAAAAUQJqZmZmZmQFASOF6FK5H/T+uR+F6FK4UQAAAAAAAAPA/MzMzMzMzEUAAAAAAAAAKQAAAAAAAAARAAAAAAAAACEAAAAAAAAAEQNejcD0K1wtAUrgehetREEAAAAAAAAAQQAAAAAAAAPA/AAAAAAAAEEAAAAAAAAAMQAAAAAAAAPg/zczMzMzM/D9cj8L1KFwHQOF6FK5H4fo/KVyPwvUoBEDNzMzMzMwQQAAAAAAAAABAAAAAAAAAAECkcD0K16MGQAAAAAAAAPg/AAAAAAAAAEAAAAAAAAAKQAAAAAAAAPQ/AAAAAAAAAEAAAAAAAAAAQAAAAAAAAAZAAAAAAAAADEAAAAAAAAAUQGZmZmZmZgJAAAAAAAAA+D/D9Shcj8L1PxSuR+F6FPo/j8L1KFyPFEAAAAAAAAAOQOF6FK5H4QRAAAAAAAAAAEAAAAAAAAAIQMP1KFyPwvk/AAAAAAAAAEAAAAAAAAAQQAAAAAAAAAxAAAAAAAAADEDD9Shcj8IQQAAAAAAAABRAAAAAAAAAAEAUrkfhehQAQAAAAAAAAABAAAAAAAAABEDXo3A9CtcJQNejcD0K1wFAAAAAAAAABEAAAAAAAAAaQJqZmZmZmfE/uB6F61G4CEDXo3A9CtcLQAAAAAAAAAhAAAAAAAAABEAAAAAAAAAAQArXo3A9CgdArkfhehSuEkAAAAAAAAAAQAAAAAAAAAhA", "dtype": "f8"}, "yaxis": "y"}, {"boxpoints": "all", "customdata": [["Female", "No", "Sun", "Dinner", 2], ["Female", "No", "Sun", "Dinner", 4], ["Female", "No", "Sun", "Dinner", 4], ["Female", "No", "Sun", "Dinner", 2], ["Female", "No", "Sun", "Dinner", 3], ["Female", "No", "Sun", "Dinner", 3], ["Female", "No", "Sat", "Dinner", 2], ["Female", "No", "Sat", "Dinner", 2], ["Female", "No", "Sat", "Dinner", 2], ["Female", "No", "Sat", "Dinner", 2], ["Female", "No", "Sat", "Dinner", 4], ["Female", "No", "Sat", "Dinner", 3], ["Female", "No", "Sun", "Dinner", 2], ["Female", "No", "Sun", "Dinner", 4], ["Female", "No", "Sat", "Dinner", 2], ["Female", "No", "Sat", "Dinner", 2], ["Female", "Yes", "Sat", "Dinner", 1], ["Female", "No", "Sat", "Dinner", 3], ["Female", "Yes", "Sat", "Dinner", 2], ["Female", "Yes", "Sat", "Dinner", 2], ["Female", "No", "Sat", "Dinner", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 1], ["Female", "No", "<PERSON><PERSON>", "Lunch", 4], ["Female", "Yes", "<PERSON><PERSON>", "Dinner", 2], ["Female", "Yes", "<PERSON><PERSON>", "Dinner", 2], ["Female", "No", "<PERSON><PERSON>", "Dinner", 2], ["Female", "Yes", "<PERSON><PERSON>", "Dinner", 2], ["Female", "Yes", "<PERSON><PERSON>", "Dinner", 2], ["Female", "Yes", "Sat", "Dinner", 3], ["Female", "Yes", "Sat", "Dinner", 2], ["Female", "No", "Sat", "Dinner", 2], ["Female", "Yes", "Sat", "Dinner", 2], ["Female", "No", "Sat", "Dinner", 1], ["Female", "No", "Sun", "Dinner", 3], ["Female", "No", "Sun", "Dinner", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 4], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 6], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 6], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 3], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "Sun", "Dinner", 5], ["Female", "No", "Sun", "Dinner", 4], ["Female", "No", "Sun", "Dinner", 2], ["Female", "No", "Sun", "Dinner", 3], ["Female", "Yes", "Sun", "Dinner", 2], ["Female", "Yes", "Sat", "Dinner", 2], ["Female", "Yes", "Sat", "Dinner", 2], ["Female", "Yes", "Sun", "Dinner", 2], ["Female", "Yes", "Sun", "Dinner", 3], ["Female", "Yes", "Sun", "Dinner", 3], ["Female", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Female", "Yes", "<PERSON><PERSON>", "Lunch", 4], ["Female", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Female", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Female", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Female", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Female", "Yes", "<PERSON><PERSON>", "Lunch", 3], ["Female", "Yes", "Sat", "Dinner", 2], ["Female", "Yes", "Sat", "Dinner", 2], ["Female", "Yes", "Sat", "Dinner", 3], ["Female", "Yes", "Sat", "Dinner", 2], ["Female", "Yes", "Sat", "Dinner", 4], ["Female", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 3], ["Female", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Female", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Female", "Yes", "Sat", "Dinner", 2], ["Female", "No", "Sat", "Dinner", 3], ["Female", "Yes", "Sat", "Dinner", 2], ["Female", "No", "<PERSON><PERSON>", "Dinner", 2]], "fillcolor": "rgba(255,255,255,0)", "hoveron": "points", "hovertemplate": "sex=%{customdata[0]}<br>total_bill=%{x}<br>smoker=%{customdata[1]}<br>day=%{customdata[2]}<br>time=%{customdata[3]}<br>size=%{customdata[4]}<extra></extra>", "jitter": 0, "legendgroup": "Female", "line": {"color": "rgba(255,255,255,0)"}, "marker": {"color": "#636efa", "symbol": "line-ns-open"}, "name": "Female", "showlegend": false, "type": "box", "x": {"bdata": "PQrXo3D9MEDXo3A9Cpc4QOF6FK5HoUFAKVyPwvWoLUApXI/C9agkQLgehetR+DBACtejcD1KNEAK16NwPYovQGZmZmZmpjNAH4XrUbgeLkBxPQrXo7A0QK5H4XoU7jBAFK5H4XqUJEBI4XoUrmdBQClcj8L1aDpAMzMzMzNzMECPwvUoXI8IQFK4HoXrETFAXI/C9SjcOkBI4XoUrkc5QPYoXI/CdS1ApHA9CtcjJEAK16NwPWpBQAAAAAAAABdAUrgehetRMEAAAAAAAMA2QDMzMzMzsyZAw/UoXI/CLkBmZmZmZiZGQOxRuB6FazZA7FG4HoXrNEAfhetRuJ4sQAAAAAAAAB1A9ihcj8K1OUCPwvUoXE8xQM3MzMzMTCVAXI/C9SjcKEAUrkfhehQ4QNejcD0K1ypA9ihcj8L1KEDNzMzMzMw9QArXo3A9Ci1Aw/UoXI/CJkCF61G4HkU0QNejcD0KVyZAhetRuB6FKEDD9Shcj0IyQIXrUbgeBSFAKVyPwvWoJEDNzMzMzEwsQFK4HoXrUSpAuB6F61F4MUDNzMzMzAw7QK5H4XoUbjBAMzMzMzOzIECkcD0K16MyQD0K16NwvSdAmpmZmZnZPUAAAAAAAAA5QEjhehSuxypA9ihcj8I1MEDD9Shcj4IxQK5H4XoULiVAw/UoXI9CJUAzMzMzMzMjQGZmZmZm5jRAZmZmZmYmMkCPwvUoXM8zQK5H4XoUjkVAAAAAAAAAKkB7FK5H4XopQAAAAAAAACpAZmZmZmZmMEC4HoXrUXgwQIXrUbgehSlACtejcD2KKkDsUbgehSs8QM3MzMzMzClApHA9CtcjPkDXo3A9CtcqQPYoXI/C9S9AhetRuB5FMECuR+F6FC4kQB+F61G4HjZACtejcD3qQUCuR+F6FC47QEjhehSuxzJA", "dtype": "f8"}, "xaxis": "x2", "yaxis": "y2"}, {"bingroup": "x", "histfunc": "sum", "hovertemplate": "sex=Male<br>total_bill=%{x}<br>sum of tip=%{y}<extra></extra>", "legendgroup": "Male", "marker": {"color": "#EF553B", "pattern": {"shape": ""}}, "name": "Male", "orientation": "v", "showlegend": true, "type": "histogram", "x": {"bdata": "rkfhehSuJEDD9ShcjwI1QK5H4XoUrjdACtejcD1KOUAK16NwPYohQOF6FK5H4TpAFK5H4XoULkCPwvUoXI8tQArXo3A9iiRA16NwPQrXLkCuR+F6FG4yQBSuR+F6lDVACtejcD1KMEBmZmZmZqY0QOxRuB6F6zFA9ihcj8K1Q0BSuB6F69EzQI/C9ShczzFAPQrXo3C9KkDhehSuR2EpQDMzMzMzszVAmpmZmZkZI0CamZmZmVkyQEjhehSuxzFAj8L1KFwPOECPwvUoXE8wQHE9CtejsDJAhetRuB5FP0AK16NwPQowQPYoXI/CdTFA4XoUrkfhK0Bcj8L1KFwjQGZmZmZmZj5ACtejcD1KMkB7FK5H4To2QDMzMzMzM0BAzczMzMyMPEAK16NwPQoyQBSuR+F6FClA4XoUrkfhI0CPwvUoXI85QD0K16NwfTNA4XoUrkcBQ0B7FK5H4XomQMP1KFyPIkhACtejcD1KNEAfhetRuJ4rQArXo3A9CiZACtejcD1KMkDXo3A9CpcxQBSuR+F6FDRAexSuR+E6NECF61G4HgUuQArXo3A9CihAhetRuB4FJUDsUbgehesxQDMzMzMzMztAw/UoXI/CNkAK16NwPUoxQHE9CtejcDNAKVyPwvWoMEDXo3A9CldAQPYoXI/C9S9Aj8L1KFwPKkBI4XoUrkcyQPYoXI/CtThAKVyPwvUoNUC4HoXrUfg8QD0K16NwfTZA9ihcj8IVREBI4XoUrkc7QI/C9ShcDyhAw/UoXI8CNUDsUbgehesoQLgehetRuC5APQrXo3B9NED2KFyPwjU5QD0K16NwPTJAAAAAAAAALEApXI/C9QhDQDMzMzMz8zdArkfhehTuPUDhehSuR2EnQIXrUbgehSxAZmZmZmbmL0AK16NwPQohQFK4HoXr0TZAFK5H4XoUM0AAAAAAAAAwQGZmZmZmJkFAuB6F61GYRECPwvUoXI8jQArXo3A9Ch5ApHA9CtcjLEDD9Shcj0IqQMP1KFyPQjFAzczMzMyMOECF61G4HsUzQPYoXI/CFUhAPQrXo3B9MEAAAAAAAIA1QFK4HoXrUSlAH4XrUbieK0CF61G4HoU4QMP1KFyPwjRA9ihcj8K1P0BI4XoUrmdJQB+F61G4ni9AAAAAAAAAHUCamZmZmdk/QFK4HoXr0TBAMzMzMzNzQECkcD0K1+MxQPYoXI/C9SxAcT0K16NQQUAzMzMzM1NBQBSuR+F6VDdAzczMzMysRkDsUbgehSs3QGZmZmZmRkRAcT0K16OwNED2KFyPwnU+QJqZmZmZGTdA4XoUrkdhL0BxPQrXo3A8QPYoXI/C9S5AFK5H4XqUMEA9CtejcD0eQK5H4XoUriRAhetRuB4FK0D2KFyPwrUyQEjhehSuhzRA16NwPQqXOkA9CtejcF1DQIXrUbgeRThAj8L1KFwPPkCkcD0K1+M5QArXo3A9KkhAZmZmZmYmPECuR+F6FC4nQPYoXI/C9R5AUrgehetRKEApXI/C9SghQNejcD0K1ypAMzMzMzNzNECPwvUoXI8qQMP1KFyPAjhA4XoUrkdhL0C4HoXrUTgnQArXo3A9iiVAj8L1KFwPL0CkcD0K1yMkQDMzMzMzMylACtejcD1qQEBI4XoUrgc9QOxRuB6FqzZAUrgehevRMUA=", "dtype": "f8"}, "xaxis": "x", "y": {"bdata": "j8L1KFyP+j8AAAAAAAAMQHsUrkfhegpA16NwPQrXEkAAAAAAAAAAQPYoXI/C9QhAXI/C9Shc/z/Xo3A9CtcJQFyPwvUoXPs/H4XrUbge+T8AAAAAAAAIQFyPwvUoXA9ArkfhehSuDUDNzMzMzMwKQFK4HoXrURBAUrgehetRHkBxPQrXo3AJQLgehetRuAJAAAAAAAAAAEAAAAAAAAAAQDMzMzMzMxFAMzMzMzMz9z8AAAAAAAAEQClcj8L1KApAzczMzMzMDEAAAAAAAAAAQHsUrkfhegJAAAAAAAAAFEDsUbgehesBQFK4HoXrUQRAexSuR+F6CEAfhetRuB71P2ZmZmZmZhZAAAAAAAAACEAAAAAAAAAUQAAAAAAAABhAZmZmZmZmAEAAAAAAAAAIQAAAAAAAAARA9ihcj8L1+D9cj8L1KFwRQBSuR+F6FAxAAAAAAAAACEApXI/C9Sj8P+xRuB6F6xpArkfhehSuCUAAAAAAAAAAQK5H4XoUrv8/FK5H4XoUDkAfhetRuB4FQDMzMzMzMwlAFK5H4XoUAEC4HoXrUbgAQIXrUbgehf8/AAAAAAAA9D+kcD0K16MIQAAAAAAAABBAAAAAAAAACECuR+F6FK4FQAAAAAAAAAhAMzMzMzMzC0AAAAAAAAAUQD0K16NwPQBAAAAAAAAAAEAAAAAAAAAQQGZmZmZmZhdAAAAAAAAACEAAAAAAAAAIQAAAAAAAAAxA7FG4HoXrEkAAAAAAAAAQQAAAAAAAAPg/AAAAAAAACEAAAAAAAAD4Pz0K16NwPfo/PQrXo3A9EEApXI/C9SgRQBSuR+F6FA5AAAAAAAAACEAAAAAAAAAQQGZmZmZmZgRASOF6FK5HFEB7FK5H4XoCQAAAAAAAAARAAAAAAAAAAECuR+F6FK73P3E9CtejcAFAAAAAAAAA+D8AAAAAAAAAQM3MzMzMzBpAAAAAAAAAFECuR+F6FK77PwAAAAAAAABAAAAAAAAABEAAAAAAAAAAQOxRuB6F6wVAAAAAAAAAAEAAAAAAAAAAQAAAAAAAABRAAAAAAAAAAEAAAAAAAAAMQAAAAAAAAARAAAAAAAAAAEDXo3A9CtcLQOxRuB6F6wFAAAAAAAAAEkAAAAAAAAAkQEjhehSuRwlAmpmZmZmZFEBxPQrXo3AJQAAAAAAAABBA4XoUrkfhCEAAAAAAAAAAQAAAAAAAAABAZmZmZmZmDEBxPQrXo3ANQJqZmZmZmRZAAAAAAAAADEAAAAAAAAAaQAAAAAAAAAhAAAAAAAAAFEAAAAAAAAAAQAAAAAAAABBAAAAAAAAA+D97FK5H4XoEQClcj8L1KABAAAAAAAAAEEAK16NwPQr3PwAAAAAAAABAAAAAAAAAAEAAAAAAAAAQQAAAAAAAABBASOF6FK5HC0AAAAAAAAAIQD0K16NwPQBAAAAAAAAAAECkcD0K16MUQAAAAAAAACJAAAAAAAAACEAAAAAAAAD4PwrXo3A9Cvc/mpmZmZmZAUC4HoXrUbj+P0jhehSuR/k/AAAAAAAACEDD9Shcj8IFQAAAAAAAAABAAAAAAAAACEAfhetRuB4LQIXrUbgehfc/AAAAAAAACEAAAAAAAAD0PwAAAAAAAPA/uB6F61G48j+uR+F6FK4XQAAAAAAAAABAAAAAAAAA/D8=", "dtype": "f8"}, "yaxis": "y"}, {"boxpoints": "all", "customdata": [["Male", "No", "Sun", "Dinner", 3], ["Male", "No", "Sun", "Dinner", 3], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 4], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 4], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 4], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 3], ["Male", "No", "Sat", "Dinner", 3], ["Male", "No", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 4], ["Male", "No", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 4], ["Male", "No", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 4], ["Male", "No", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 3], ["Male", "No", "Sat", "Dinner", 3], ["Male", "No", "Sat", "Dinner", 3], ["Male", "No", "Sat", "Dinner", 3], ["Male", "No", "Sat", "Dinner", 3], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 4], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 4], ["Male", "No", "Sun", "Dinner", 3], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 4], ["Male", "No", "Sun", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 4], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 4], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 4], ["Male", "No", "Sat", "Dinner", 3], ["Male", "No", "Sat", "Dinner", 3], ["Male", "No", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 4], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "Yes", "<PERSON><PERSON>", "Dinner", 2], ["Male", "No", "<PERSON><PERSON>", "Dinner", 2], ["Male", "Yes", "<PERSON><PERSON>", "Dinner", 4], ["Male", "Yes", "<PERSON><PERSON>", "Dinner", 2], ["Male", "Yes", "<PERSON><PERSON>", "Dinner", 2], ["Male", "Yes", "<PERSON><PERSON>", "Dinner", 2], ["Male", "No", "<PERSON><PERSON>", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 3], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 4], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 3], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 6], ["Male", "No", "<PERSON><PERSON>", "Lunch", 5], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 3], ["Male", "No", "Sun", "Dinner", 4], ["Male", "No", "Sun", "Dinner", 4], ["Male", "No", "Sun", "Dinner", 6], ["Male", "No", "Sun", "Dinner", 4], ["Male", "No", "Sun", "Dinner", 4], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 3], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 4], ["Male", "Yes", "Sat", "Dinner", 3], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "Yes", "Sun", "Dinner", 2], ["Male", "Yes", "Sun", "Dinner", 2], ["Male", "Yes", "Sun", "Dinner", 2], ["Male", "Yes", "Sun", "Dinner", 2], ["Male", "Yes", "Sun", "Dinner", 2], ["Male", "Yes", "Sun", "Dinner", 2], ["Male", "Yes", "Sun", "Dinner", 2], ["Male", "Yes", "Sun", "Dinner", 4], ["Male", "Yes", "Sun", "Dinner", 2], ["Male", "Yes", "Sun", "Dinner", 3], ["Male", "Yes", "Sun", "Dinner", 4], ["Male", "Yes", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 5], ["Male", "Yes", "Sun", "Dinner", 5], ["Male", "Yes", "Sun", "Dinner", 3], ["Male", "Yes", "Sun", "Dinner", 2], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 3], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 4], ["Male", "Yes", "Sat", "Dinner", 3], ["Male", "Yes", "Sat", "Dinner", 4], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 3], ["Male", "Yes", "Sat", "Dinner", 4], ["Male", "No", "Sat", "Dinner", 4], ["Male", "Yes", "Sat", "Dinner", 5], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 1], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "Sat", "Dinner", 4], ["Male", "No", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 4], ["Male", "Yes", "Sat", "Dinner", 3], ["Male", "No", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 3], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 2]], "fillcolor": "rgba(255,255,255,0)", "hoveron": "points", "hovertemplate": "sex=%{customdata[0]}<br>total_bill=%{x}<br>smoker=%{customdata[1]}<br>day=%{customdata[2]}<br>time=%{customdata[3]}<br>size=%{customdata[4]}<extra></extra>", "jitter": 0, "legendgroup": "Male", "line": {"color": "rgba(255,255,255,0)"}, "marker": {"color": "#EF553B", "symbol": "line-ns-open"}, "name": "Male", "showlegend": false, "type": "box", "x": {"bdata": "rkfhehSuJEDD9ShcjwI1QK5H4XoUrjdACtejcD1KOUAK16NwPYohQOF6FK5H4TpAFK5H4XoULkCPwvUoXI8tQArXo3A9iiRA16NwPQrXLkCuR+F6FG4yQBSuR+F6lDVACtejcD1KMEBmZmZmZqY0QOxRuB6F6zFA9ihcj8K1Q0BSuB6F69EzQI/C9ShczzFAPQrXo3C9KkDhehSuR2EpQDMzMzMzszVAmpmZmZkZI0CamZmZmVkyQEjhehSuxzFAj8L1KFwPOECPwvUoXE8wQHE9CtejsDJAhetRuB5FP0AK16NwPQowQPYoXI/CdTFA4XoUrkfhK0Bcj8L1KFwjQGZmZmZmZj5ACtejcD1KMkB7FK5H4To2QDMzMzMzM0BAzczMzMyMPEAK16NwPQoyQBSuR+F6FClA4XoUrkfhI0CPwvUoXI85QD0K16NwfTNA4XoUrkcBQ0B7FK5H4XomQMP1KFyPIkhACtejcD1KNEAfhetRuJ4rQArXo3A9CiZACtejcD1KMkDXo3A9CpcxQBSuR+F6FDRAexSuR+E6NECF61G4HgUuQArXo3A9CihAhetRuB4FJUDsUbgehesxQDMzMzMzMztAw/UoXI/CNkAK16NwPUoxQHE9CtejcDNAKVyPwvWoMEDXo3A9CldAQPYoXI/C9S9Aj8L1KFwPKkBI4XoUrkcyQPYoXI/CtThAKVyPwvUoNUC4HoXrUfg8QD0K16NwfTZA9ihcj8IVREBI4XoUrkc7QI/C9ShcDyhAw/UoXI8CNUDsUbgehesoQLgehetRuC5APQrXo3B9NED2KFyPwjU5QD0K16NwPTJAAAAAAAAALEApXI/C9QhDQDMzMzMz8zdArkfhehTuPUDhehSuR2EnQIXrUbgehSxAZmZmZmbmL0AK16NwPQohQFK4HoXr0TZAFK5H4XoUM0AAAAAAAAAwQGZmZmZmJkFAuB6F61GYRECPwvUoXI8jQArXo3A9Ch5ApHA9CtcjLEDD9Shcj0IqQMP1KFyPQjFAzczMzMyMOECF61G4HsUzQPYoXI/CFUhAPQrXo3B9MEAAAAAAAIA1QFK4HoXrUSlAH4XrUbieK0CF61G4HoU4QMP1KFyPwjRA9ihcj8K1P0BI4XoUrmdJQB+F61G4ni9AAAAAAAAAHUCamZmZmdk/QFK4HoXr0TBAMzMzMzNzQECkcD0K1+MxQPYoXI/C9SxAcT0K16NQQUAzMzMzM1NBQBSuR+F6VDdAzczMzMysRkDsUbgehSs3QGZmZmZmRkRAcT0K16OwNED2KFyPwnU+QJqZmZmZGTdA4XoUrkdhL0BxPQrXo3A8QPYoXI/C9S5AFK5H4XqUMEA9CtejcD0eQK5H4XoUriRAhetRuB4FK0D2KFyPwrUyQEjhehSuhzRA16NwPQqXOkA9CtejcF1DQIXrUbgeRThAj8L1KFwPPkCkcD0K1+M5QArXo3A9KkhAZmZmZmYmPECuR+F6FC4nQPYoXI/C9R5AUrgehetRKEApXI/C9SghQNejcD0K1ypAMzMzMzNzNECPwvUoXI8qQMP1KFyPAjhA4XoUrkdhL0C4HoXrUTgnQArXo3A9iiVAj8L1KFwPL0CkcD0K1yMkQDMzMzMzMylACtejcD1qQEBI4XoUrgc9QOxRuB6FqzZAUrgehevRMUA=", "dtype": "f8"}, "xaxis": "x2", "yaxis": "y2"}], "layout": {"barmode": "relative", "legend": {"title": {"text": "sex"}, "tracegroupgap": 0}, "margin": {"t": 60}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "xaxis": {"anchor": "y", "domain": [0, 1], "title": {"text": "total_bill"}}, "xaxis2": {"anchor": "y2", "domain": [0, 1], "matches": "x", "showgrid": true, "showticklabels": false}, "yaxis": {"anchor": "x", "domain": [0, 0.7326], "title": {"text": "sum of tip"}}, "yaxis2": {"anchor": "x2", "domain": [0.7426, 1], "matches": "y2", "showgrid": false, "showline": false, "showticklabels": false, "ticks": ""}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["import plotly.express as px\n", "df = px.data.tips()\n", "fig = px.histogram(df, x=\"total_bill\", y=\"tip\", color=\"sex\", marginal=\"rug\",\n", "                    hover_data=df.columns)\n", "fig.show()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ddac93dbfb814700870652258d462129", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(Figure(axes=[Axis(scale=LinearScale()), Axis(orientation='vertical', scale=LinearScale())], fig…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "size = 100\n", "np.random.seed(0)\n", "x_data = np.arange(size)\n", "y_data = np.cumsum(np.random.randn(size) * 100.0)\n", "from bqplot import pyplot as plt\n", "plt.figure(title=\"My First Plot\")\n", "plt.plot(x_data, y_data)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "<script>\n", "    require.config({\n", "        paths: {\n", "            'echarts':'https://assets.pyecharts.org/assets/v5/echarts.min'\n", "        }\n", "    });\n", "</script>\n", "\n", "        <div id=\"815303693eab488d9bcaff5984a7cc69\" style=\"width:900px; height:500px;\"></div>\n", "\n", "<script>\n", "        require(['echarts'], function(echarts) {\n", "                var chart_815303693eab488d9bcaff5984a7cc69 = echarts.init(\n", "                    document.getElementById('815303693eab488d9bcaff5984a7cc69'), 'white', {renderer: 'canvas'});\n", "                var option_815303693eab488d9bcaff5984a7cc69 = {\n", "    \"animation\": true,\n", "    \"animationThreshold\": 2000,\n", "    \"animationDuration\": 1000,\n", "    \"animationEasing\": \"cubicOut\",\n", "    \"animationDelay\": 0,\n", "    \"animationDurationUpdate\": 300,\n", "    \"animationEasingUpdate\": \"cubicOut\",\n", "    \"animationDelayUpdate\": 0,\n", "    \"aria\": {\n", "        \"enabled\": false\n", "    },\n", "    \"color\": [\n", "        \"#5470c6\",\n", "        \"#91cc75\",\n", "        \"#fac858\",\n", "        \"#ee6666\",\n", "        \"#73c0de\",\n", "        \"#3ba272\",\n", "        \"#fc8452\",\n", "        \"#9a60b4\",\n", "        \"#ea7ccc\"\n", "    ],\n", "    \"series\": [\n", "        {\n", "            \"type\": \"bar\",\n", "            \"name\": \"\\u5546\\u5bb6A\",\n", "            \"legendHoverLink\": true,\n", "            \"data\": [\n", "                114,\n", "                55,\n", "                27,\n", "                101,\n", "                125,\n", "                27,\n", "                105\n", "            ],\n", "            \"realtimeSort\": false,\n", "            \"showBackground\": false,\n", "            \"stackStrategy\": \"samesign\",\n", "            \"cursor\": \"pointer\",\n", "            \"barMinHeight\": 0,\n", "            \"barCategoryGap\": \"20%\",\n", "            \"barGap\": \"30%\",\n", "            \"large\": false,\n", "            \"largeThreshold\": 400,\n", "            \"seriesLayoutBy\": \"column\",\n", "            \"datasetIndex\": 0,\n", "            \"clip\": true,\n", "            \"zlevel\": 0,\n", "            \"z\": 2,\n", "            \"label\": {\n", "                \"show\": true,\n", "                \"margin\": 8,\n", "                \"valueAnimation\": false\n", "            }\n", "        },\n", "        {\n", "            \"type\": \"bar\",\n", "            \"name\": \"\\u5546\\u5bb6B\",\n", "            \"legendHoverLink\": true,\n", "            \"data\": [\n", "                57,\n", "                134,\n", "                137,\n", "                129,\n", "                145,\n", "                60,\n", "                49\n", "            ],\n", "            \"realtimeSort\": false,\n", "            \"showBackground\": false,\n", "            \"stackStrategy\": \"samesign\",\n", "            \"cursor\": \"pointer\",\n", "            \"barMinHeight\": 0,\n", "            \"barCategoryGap\": \"20%\",\n", "            \"barGap\": \"30%\",\n", "            \"large\": false,\n", "            \"largeThreshold\": 400,\n", "            \"seriesLayoutBy\": \"column\",\n", "            \"datasetIndex\": 0,\n", "            \"clip\": true,\n", "            \"zlevel\": 0,\n", "            \"z\": 2,\n", "            \"label\": {\n", "                \"show\": true,\n", "                \"margin\": 8,\n", "                \"valueAnimation\": false\n", "            }\n", "        }\n", "    ],\n", "    \"legend\": [\n", "        {\n", "            \"data\": [\n", "                \"\\u5546\\u5bb6A\",\n", "                \"\\u5546\\u5bb6B\"\n", "            ],\n", "            \"selected\": {},\n", "            \"show\": true,\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"itemWidth\": 25,\n", "            \"itemHeight\": 14,\n", "            \"backgroundColor\": \"transparent\",\n", "            \"borderColor\": \"#ccc\",\n", "            \"borderRadius\": 0,\n", "            \"pageButtonItemGap\": 5,\n", "            \"pageButtonPosition\": \"end\",\n", "            \"pageFormatter\": \"{current}/{total}\",\n", "            \"pageIconColor\": \"#2f4554\",\n", "            \"pageIconInactiveColor\": \"#aaa\",\n", "            \"pageIconSize\": 15,\n", "            \"animationDurationUpdate\": 800,\n", "            \"selector\": false,\n", "            \"selectorPosition\": \"auto\",\n", "            \"selectorItemGap\": 7,\n", "            \"selectorButtonGap\": 10\n", "        }\n", "    ],\n", "    \"tooltip\": {\n", "        \"show\": true,\n", "        \"trigger\": \"item\",\n", "        \"triggerOn\": \"mousemove|click\",\n", "        \"axisPointer\": {\n", "            \"type\": \"line\"\n", "        },\n", "        \"showContent\": true,\n", "        \"alwaysShowContent\": false,\n", "        \"showDelay\": 0,\n", "        \"hideDelay\": 100,\n", "        \"enterable\": false,\n", "        \"confine\": false,\n", "        \"appendToBody\": false,\n", "        \"transitionDuration\": 0.4,\n", "        \"textStyle\": {\n", "            \"fontSize\": 14\n", "        },\n", "        \"borderWidth\": 0,\n", "        \"padding\": 5,\n", "        \"order\": \"seriesAsc\"\n", "    },\n", "    \"xAxis\": [\n", "        {\n", "            \"show\": true,\n", "            \"scale\": false,\n", "            \"nameLocation\": \"end\",\n", "            \"nameGap\": 15,\n", "            \"gridIndex\": 0,\n", "            \"inverse\": false,\n", "            \"offset\": 0,\n", "            \"splitNumber\": 5,\n", "            \"minInterval\": 0,\n", "            \"splitLine\": {\n", "                \"show\": true,\n", "                \"lineStyle\": {\n", "                    \"show\": true,\n", "                    \"width\": 1,\n", "                    \"opacity\": 1,\n", "                    \"curveness\": 0,\n", "                    \"type\": \"solid\"\n", "                }\n", "            },\n", "            \"animation\": true,\n", "            \"animationThreshold\": 2000,\n", "            \"animationDuration\": 1000,\n", "            \"animationEasing\": \"cubicOut\",\n", "            \"animationDelay\": 0,\n", "            \"animationDurationUpdate\": 300,\n", "            \"animationEasingUpdate\": \"cubicOut\",\n", "            \"animationDelayUpdate\": 0,\n", "            \"data\": [\n", "                \"\\u886c\\u886b\",\n", "                \"\\u6bdb\\u8863\",\n", "                \"\\u9886\\u5e26\",\n", "                \"\\u88e4\\u5b50\",\n", "                \"\\u98ce\\u8863\",\n", "                \"\\u9ad8\\u8ddf\\u978b\",\n", "                \"\\u889c\\u5b50\"\n", "            ]\n", "        }\n", "    ],\n", "    \"yAxis\": [\n", "        {\n", "            \"show\": true,\n", "            \"scale\": false,\n", "            \"nameLocation\": \"end\",\n", "            \"nameGap\": 15,\n", "            \"gridIndex\": 0,\n", "            \"inverse\": false,\n", "            \"offset\": 0,\n", "            \"splitNumber\": 5,\n", "            \"minInterval\": 0,\n", "            \"splitLine\": {\n", "                \"show\": true,\n", "                \"lineStyle\": {\n", "                    \"show\": true,\n", "                    \"width\": 1,\n", "                    \"opacity\": 1,\n", "                    \"curveness\": 0,\n", "                    \"type\": \"solid\"\n", "                }\n", "            },\n", "            \"animation\": true,\n", "            \"animationThreshold\": 2000,\n", "            \"animationDuration\": 1000,\n", "            \"animationEasing\": \"cubicOut\",\n", "            \"animationDelay\": 0,\n", "            \"animationDurationUpdate\": 300,\n", "            \"animationEasingUpdate\": \"cubicOut\",\n", "            \"animationDelayUpdate\": 0\n", "        }\n", "    ],\n", "    \"title\": [\n", "        {\n", "            \"show\": true,\n", "            \"text\": \"\\u67d0\\u5546\\u573a\\u9500\\u552e\\u60c5\\u51b5\",\n", "            \"target\": \"blank\",\n", "            \"subtarget\": \"blank\",\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"textAlign\": \"auto\",\n", "            \"textVerticalAlign\": \"auto\",\n", "            \"triggerEvent\": false\n", "        }\n", "    ]\n", "};\n", "                chart_815303693eab488d9bcaff5984a7cc69.setOption(option_815303693eab488d9bcaff5984a7cc69);\n", "        });\n", "    </script>\n"], "text/plain": ["<pyecharts.render.display.HTML at 0x11722ce50>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from pyecharts.charts import Bar\n", "from pyecharts import options as opts\n", "\n", "bar = (\n", "    Bar()\n", "    .add_xaxis([\"衬衫\", \"毛衣\", \"领带\", \"裤子\", \"风衣\", \"高跟鞋\", \"袜子\"])\n", "    .add_yaxis(\"商家A\", [114, 55, 27, 101, 125, 27, 105])\n", "    .add_yaxis(\"商家B\", [57, 134, 137, 129, 145, 60, 49])\n", "    .set_global_opts(title_opts=opts.TitleOpts(title=\"某商场销售情况\"))\n", ")\n", "# for jupyterlab\n", "# bar.load_javascript()\n", "bar.render_notebook()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["<pyecharts.charts.basic_charts.bar.Bar at 0x12724ec50>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["bar"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"application/vnd.databuilder.v1+notebook": {}, "kernelspec": {"display_name": "datalayer", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 4}