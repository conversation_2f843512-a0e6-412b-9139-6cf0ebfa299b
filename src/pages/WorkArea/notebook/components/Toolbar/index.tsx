import classNames from 'classnames/bind';
import styles from './index.module.less';
import {useNotebookAction} from '../../hook';
import {Tooltip} from 'acud';
import {useHotkeys} from '@hooks/useHotkeys';
import {NotebookHotkeyContext, createToolbarHotkeys} from '../../hotkeys';

const cx = classNames.bind(styles);

interface ToolbarProps {
  notebookId: string;
  notebookName: string;
}
export default function Toolbar({notebookId, notebookName}: ToolbarProps) {
  const {save, clearAllOutputs, exportNotebook, toggleAllLineNumbers, deleteAllCells} =
    useNotebookAction(notebookId);

  // 使用快捷键系统
  useHotkeys(
    NotebookHotkeyContext.TOOLBAR,
    createToolbarHotkeys({
      save
    })
  );

  function onExport() {
    exportNotebook(notebookName);
  }

  function onToggleAllLineNumbers() {
    toggleAllLineNumbers();
  }

  const placement = 'bottom';

  return (
    <div className={cx('toolbar')}>
      <Tooltip title="保存" placement={placement}>
        <div className={cx('item', 'save')} onClick={save}></div>
      </Tooltip>
      <Tooltip title="导出" placement={placement}>
        <div className={cx('item', 'export')} onClick={onExport}></div>
      </Tooltip>
      <Tooltip title="清空单元格" placement={placement}>
        <div className={cx('item', 'delete-all-cells')} onClick={deleteAllCells}></div>
      </Tooltip>
      <Tooltip title="清空全部输出" placement={placement}>
        <div className={cx('item', 'clear-all-outputs')} onClick={clearAllOutputs}></div>
      </Tooltip>
      {/* <Tooltip title="显示行号" placement="top">
        <div className={cx('item', 'toggle-all-line-numbers')} onClick={onToggleAllLineNumbers}></div>
      </Tooltip> */}
    </div>
  );
}
