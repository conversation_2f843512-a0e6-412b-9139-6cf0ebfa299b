.compute-selector-dropdown {
  :global(.acud-btn-default:hover) {
    color: #151b26;
    border-color: #d4d6d9;
  }
  :global(.acud-dropdown-open) {
    .arrow {
      transform: rotate(0deg);
    }
  }
}

.compute-btn {
  .item-text {
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.compute-selector {
  width: 100%;
  padding: 8px;
  .split-line {
    border-bottom: 1px solid #E8E9EB;
    margin-top: 8px;
    margin-bottom: 8px;
  }
  .compute-selected {
    .selected-action {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 5px 8px;

      .connect-status {
        color: #84868C;
      }

      .disconnect {
        color: #2468F2;
        cursor: pointer;
      }
    }
  }
  .compute-list-title {
    height: 30px;
    padding: 5px 8px;
    color: #84868C;
  }

  .compute-item {
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 30px;
    padding: 5px 8px;
    margin-bottom: 4px;
    .item-text {
      max-width: 170px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .tag {
      background-color: #F7F7F9;
      color: #5C5F66;
      height: 20px;
      line-height: 20px;
      font-size: 12px;
      padding: 0 6px;
      border-radius: 4px;
      flex: none;
    }
    &:hover {
      background-color: #F7F7F9;
      border-radius: 4px;
      .tag {
        background-color: #fff;
      }
    }

    &.disabled {
      cursor: not-allowed;
      background-color: #F7F7F9;
      color: #B8BABF;
      border-radius: 4px;
      .tag {
        color: #B8BABF;
      }
      &:hover {
        .tag {
          background-color: #F7F7F9;
        }
      }
    }
  }

}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.status-icon {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 8px;

  &.none {
    background: #5C5F66;
  }

  &.running {
    background: #30BF13;
  }

  &.deploying {
    background: #2468F2;
  }

  &.invalid {
    background: #5C5F66;
  }

  &.created-fail {
    background: #F33E3E;
  }

  &.connecting {
    width: 14px;
    height: 14px;
    background-image: url("~@assets/originSvg/notebook/compute-loading.svg?url");
    background-size: 14px 14px;
    background-repeat: no-repeat;
    background-position: center;
    animation: rotate 1s linear infinite;
  }
}

.arrow {
  margin-left: 12px;
  transform: rotate(180deg);
}


