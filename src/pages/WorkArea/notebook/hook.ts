import {useNotebookStore, NotebookCommandIds} from '@baidu/db-jupyter-react/lib/components/notebook';
import {updateNotebookContent, renameFile} from '@api/WorkArea';
import useUrlState from '@ahooksjs/use-url-state';
import {useMemoizedFn, useRequest} from 'ahooks';
import {INotebookContent} from '@jupyterlab/nbformat';
import {toast} from 'acud';
import {useNotebook} from '@store/notebookStatehooks';
import {notebookMetaKey} from './config';
import {Modal} from 'acud';
import {updateSession, getComputeSessionList} from '@api/WorkArea';
import {useCallback} from 'react';
import {useBeforeUnload, useBlocker} from 'react-router-dom';
import React, {useEffect} from 'react';

export function useNotebookAction(notebookId: string) {
  const {sessionId, kernelId, computeId} = useNotebook();
  const [urlState, setUrlState] = useUrlState();
  const notebookStore = useNotebookStore();
  const notebook = notebookStore.selectNotebook(notebookId);

  const {save, saveLoading} = useNotebookSave(notebookId, urlState.workspaceId);

  const updateSessionFileName = async (fileName: string) => {
    if (!sessionId) {
      return;
    }
    const res = await getComputeSessionList({workspaceId: urlState.workspaceId});
    const session = res.result.find((item) => item.id === sessionId);
    if (session) {
      const newPath = session.path.replace(session.name, fileName);
      await updateSession({
        workspaceId: urlState.workspaceId,
        sessionId: session.id,
        payload: {
          id: session.id,
          name: fileName,
          path: newPath
        }
      });
    }
  };

  // 更新笔记本名称
  const {runAsync: updateNotebookNameRun, loading: updateNameLoading} = useRequest(renameFile, {
    manual: true,
    onSuccess: (res) => {
      if (res.success) {
        toast.success({
          message: '名称更新成功',
          duration: 5
        });
      }
    },
    onError: (error) => {
      toast.error({
        message: '名称更新失败',
        duration: 5
      });
    }
  });

  const updateNotebookName = useMemoizedFn((newName: string) => {
    return updateNotebookNameRun({
      id: notebookId,
      workspaceId: urlState.workspaceId,
      name: newName
    });
  });

  // 清除全部输出
  const clearAllOutputs = () => {
    notebook?.adapter?.commands.execute(NotebookCommandIds.clearAllOutputs);
  };

  // 导出
  const exportNotebook = (notebookName: string) => {
    const json = notebook?.adapter?.notebookPanel?.model?.toJSON();
    // 文件下载
    const blob = new Blob([JSON.stringify(json)], {type: 'application/json'});
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = notebookName || 'notebook.ipynb';
    a.click();
    URL.revokeObjectURL(url);
  };

  // 执行全部
  const runAll = () => {
    notebook?.adapter?.commands.execute(NotebookCommandIds.runAll).finally(() => {
      save();
    });
  };

  // 切换行号
  const toggleAllLineNumbers = () => {
    notebook?.adapter?.commands.execute(NotebookCommandIds.toggleAllLineNumbers);
  };

  // 删除全部
  const deleteAllCells = () => {
    Modal.confirm({
      title: '删除全部',
      content: '确定要删除全部cell吗？',
      onOk: () => {
        notebook?.adapter?.commands.execute(NotebookCommandIds.deleteAllCells);
      }
    });
  };

  return {
    save,
    saveLoading,
    clearAllOutputs,
    exportNotebook,
    runAll,
    toggleAllLineNumbers,
    deleteAllCells,
    updateNotebookName,
    updateNameLoading,
    updateSessionFileName
  };
}

export function useNotebookSave(notebookId: string, workspaceId: string, showSaveSuccess = true) {
  const notebookStore = useNotebookStore();
  const notebook = notebookStore.selectNotebook(notebookId);

  // 保存
  const {runAsync: updateNotebookContentRun, loading: saveLoading} = useRequest(updateNotebookContent, {
    manual: true,
    debounceWait: 300,
    onSuccess: (res) => {
      if (res.success) {
        if (showSaveSuccess) {
          toast.success({
            message: '保存成功',
            duration: 5
          });
        }
      }
    }
  });

  const save = useMemoizedFn(() => {
    const json = notebook?.adapter?.notebookPanel?.model?.toJSON();
    // if (sessionId && kernelId && computeId) {
    //   const metadata = json.metadata || {};
    //   metadata[notebookMetaKey] = {
    //     sessionId,
    //     kernelId,
    //     computeId
    //   };
    //   json.metadata = metadata;
    // }

    return updateNotebookContentRun({
      id: notebookId,
      workspaceId: workspaceId,
      content: json as INotebookContent
    }).then((res) => {
      if (res.success) {
        notebook.adapter.notebookPanel.model.dirty = false;
      }
    });
  });
  return {
    save,
    saveLoading
  };
}

/**
 * 用户离开页面前，检查 notebook 是否已保存，并在未保存时提示用户
 * 处理三种情况：
 * 1. 通过 react 路由跳转
 * 2. 浏览器刷新
 * 3. 浏览器页签关闭
 */
export function useNotebookUnsavedPrompt(notebookId: string) {
  const notebookStore = useNotebookStore();
  const notebook = notebookStore.selectNotebook(notebookId);

  // 检查是否有未保存的内容
  const hasUnsavedChanges = useCallback(() => {
    return notebook?.adapter?.notebookPanel?.model?.dirty || false;
  }, [notebook]);

  // 使用 useBeforeUnload 处理浏览器刷新和关闭
  useBeforeUnload(
    useCallback(
      (event) => {
        if (hasUnsavedChanges()) {
          event.preventDefault();
          return '您有未保存的更改，确定要离开吗？';
        }
        return undefined;
      },
      [hasUnsavedChanges]
    )
  );

  // 使用 useBlocker 处理 react 路由跳转
  const blocker = useBlocker(
    useCallback(
      (event) => {
        return hasUnsavedChanges();
      },
      [hasUnsavedChanges]
    )
  );

  return {
    blocker,
    hasUnsavedChanges
  };
}

export function useNotebookResize(
  ref: React.RefObject<HTMLDivElement>,
  notebookId: string,
  nbformat: INotebookContent
) {
  const notebookStore = useNotebookStore();

  useEffect(() => {
    if (ref.current && ResizeObserver) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          if (nbformat && notebookId) {
            const notebookAdapter = notebookStore.selectNotebookAdapter(notebookId);
            notebookAdapter?.notebookPanel?.update();
          }
        }
      });
      resizeObserver.observe(ref.current);
      return () => {
        resizeObserver.disconnect();
      };
    }
  }, [nbformat, notebookId, notebookStore]);
}
