/**
 * 工作流 常量
 * 记录工作流相关常量 包括枚举 常量 类型 等
 * <AUTHOR>
 */

/** 页面类型 */
export enum WorkflowPageTypeEnum {
  JOBS = 'jobs',
  TEMPLATES = 'templates'
}

// 页面类型 详情/实例
export enum JobDetailPageTypeEnum {
  DETAIL = 'detail',
  JOB_INSTANCES = 'jobInstances'
}

/** 页面类型 */
export enum JobCreateTypeEnum {
  // 新建空任务
  EMPTY = 'empty',
  // 新建 JSON 任务
  JSON = 'json'
}
/** 页面展示类型 */
export enum JobShowTypeEnum {
  // 可视化
  X6 = 'x6',
  // JSON
  JSON = 'json'
}
/** 节点类型 */
export enum JobNodeTypeEnum {
  // ray 任务
  RAY_TASK = 'RAY_TASK',
  // 算子任务
  DATAFLOW_TASK = 'DATAFLOW_TASK',
  // notebook 任务
  NOTEBOOK = 'NOTEBOOK',
  // 训练任务
  TRAIN = 'TRAIN',
  // 算子节点
  OPERATOR_NODE = 'OPERATOR_NODE'
}

// 任务节点类型
export const JobTaskType = {
  [JobNodeTypeEnum.DATAFLOW_TASK]: {
    label: '算子任务',
    value: JobNodeTypeEnum.DATAFLOW_TASK,
    icon: 'workflow-type-dataflow-task',
    color: '#3399FF'
  },
  [JobNodeTypeEnum.RAY_TASK]: {
    label: 'Ray 任务',
    value: JobNodeTypeEnum.RAY_TASK,
    icon: 'workflow-type-ray-task',
    color: '#3399FF'
  },
  [JobNodeTypeEnum.NOTEBOOK]: {
    label: 'NoteBook',
    value: JobNodeTypeEnum.NOTEBOOK,
    icon: 'workflow-type-note-book',
    color: '#3399FF'
  },
  [JobNodeTypeEnum.TRAIN]: {
    label: '训练任务',
    value: JobNodeTypeEnum.TRAIN,
    icon: 'workflow-type-train',
    color: '#3399FF'
  }
};
/**
 * 节点类型
 * 1. 边
 * 2. 任务节点
 * 3. 算子节点
 */

export enum X6ShapeTypeEnum {
  EDGE = 'edge',
  TASK = 'task-node',
  TASK_EDGE = 'task-edge',
  OPERATOR = 'operator-node',
  OPERATOR_EDGE = 'operator-edge',
  OPERATOR_GROUP = 'operator-group-node'
}

/** 页面弹窗配置类型 */
export enum JobModalTypeEnum {
  // 新建 cron
  CRON = 'cron',
  // 新建 运行实例
  RUN_INSTANCE = 'runInstance',

  // 新建空任务
  EMPTY = 'empty',
  // 新建 JSON 任务
  JSON = 'json'
}

/** cron 类型 */
export enum CronTypeEnum {
  HOUR = 'hour',
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  YEAR = 'year',
  OTHER = 'other'
}

/** cron 类型中文 */
export const CronTypeChinese = {
  [CronTypeEnum.HOUR]: '每小时',
  [CronTypeEnum.DAY]: '每天',
  [CronTypeEnum.WEEK]: '每周',
  [CronTypeEnum.MONTH]: '每月',
  [CronTypeEnum.YEAR]: '每年',
  [CronTypeEnum.OTHER]: 'CRON 表达式'
};

/** 任务调度状态 */
export enum JobScheduleStatusEnum {
  ON = 'ON',
  OFF = 'OFF',
  PENDING = 'PENDING'
}

/** 任务调度状态中文 */
export const JobScheduleStatusChinese = {
  [JobScheduleStatusEnum.ON]: '开启',
  [JobScheduleStatusEnum.OFF]: '关闭',
  [JobScheduleStatusEnum.PENDING]: '等待中'
};

/** cron 表单 key */
export enum CronItemTypeEnum {
  TIME = 'time',
  MINUTE = 'minute',
  HOUR = 'hour',
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  OTHER = 'crontab'
}

// 星期
export const CronWeekArr = [
  {
    value: 1,
    label: '周一'
  },
  {
    value: 2,
    label: '周二'
  },
  {
    value: 3,
    label: '周三'
  },
  {
    value: 4,
    label: '周四'
  },
  {
    value: 5,
    label: '周五'
  },
  {
    value: 6,
    label: '周六'
  },
  {
    value: 7,
    label: '周日'
  }
];

/** 创建 类型 */
export enum WorkflowPageCreateTypeEnum {
  COPY = 'copy',
  TEMPLATE = 'template'
}

// 节点大小 位置
export const NODE_SIZE = {
  [X6ShapeTypeEnum.TASK]: {
    width: 240,
    height: 44,
    // 任务节点 上下左右 预留间距
    padding: {top: 60, bottom: 30, left: 30, right: 30},
    // 任务节点 上下间距
    ranksep: 30,
    // 任务节点 左右间距
    nodesep: 20,
    titleHeight: 52,
    gutter: 60,
    zIndex: 1
  },
  [X6ShapeTypeEnum.OPERATOR]: {
    width: 176,
    height: 32,
    // 算子节点 上下间距
    ranksep: 8,
    // 算子节点 左右间距
    nodesep: 10,
    gutter: 24,
    paddingLeft: 12,
    zIndex: 4
  },
  [X6ShapeTypeEnum.OPERATOR_GROUP]: {
    padding: 12,
    titleHeight: 24,
    zIndex: 2
  },
  [X6ShapeTypeEnum.TASK_EDGE]: {
    zIndex: 0
  },
  [X6ShapeTypeEnum.OPERATOR_EDGE]: {
    zIndex: 3
  }
};

/** 图表配置 */
export const GraphOptions = {
  zoomToFit: {
    padding: 50,
    maxScale: 1,
    minScale: 0.5
  }
};

// 右侧展示类型
export enum RightDrawerTypeEnum {
  INIT = 'init',
  JOB_CONFIG = 'jobConfig',
  TASK_CONFIG = 'taskConfig',
  OPERATOR_CONFIG = 'operatorConfig'
}

// 左侧拖拽类型
export enum LeftDragTypeEnum {
  DEFAULT = 'default',
  OPERATOR = 'operator'
}

// 选中节点类型
export enum SelectJobNodeTypeEnum {
  // 空
  NULL = 'null',
  // 点击
  CLICK = 'click',
  // 搜索
  SEARCH = 'search',
  // 编辑 算子
  EDIT_OPERATOR = 'editOperator',
  // 保存 算子
  SAVE_OPERATOR = 'saveOperator',
  // 复制
  COPY = 'copy',
  // 删除
  DELETE = 'delete'
}

// 分隔符
export const SPLIT_STR = '-';

// json 格式化
export const JSON_FORMAT = 2;

// 缩放按钮类型
export enum X6BtnArrTypeEnum {
  AUTO_LAYOUT = 'autoLayout',
  FOCUS_CENTER = 'focusCenter'
}

// 算子 类别
export enum OperatorCategoryEnum {
  EXTRACT = 'EXTRACT',
  TRANSFORM = 'TRANSFORM',
  FILTER = 'FILTER',
  DEDUP = 'DEDUP',
  EMBEDDING = 'EMBEDDING',
  OTHERS = 'OTHERS',
  SOURCE = 'SOURCE',
  SINK = 'SINK'
}
// 算子 类别 中文
export enum OperatorCategoryChineseEnum {
  EXTRACT = '抽取',
  TRANSFORM = '处理',
  FILTER = '过滤',
  DEDUP = '去重',
  EMBEDDING = '嵌入',
  OTHERS = '其他',
  SOURCE = '输入',
  SINK = '输出'
}
// 算子 过滤类型
export enum OperatorFieldEnum {
  TEXT = 'Text',
  IMAGE = 'Image',
  AUDIO = 'Audio',
  VIDEO = 'Video',
  MULTIMODAL = 'Multimodal',
  GENERAL = 'General'
}
// 算子 过滤类型 中文
export enum OperatorFieldChineseEnum {
  Text = '文本',
  Image = '图片',
  Audio = '音频',
  Video = '视频',
  Multimodal = '多模态',
  General = '通用'
}
