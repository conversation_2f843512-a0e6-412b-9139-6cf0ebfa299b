/**
 * 处理 x6 数据
 *
 * 将 json 数据格式 转为 x6 数据格式
 * 将 x6 数据格式 转为 json 数据格式
 */

import {IOperVersionOneRes} from '@api/metaRequest';
import {
  JobNodeTypeEnum,
  JSON_FORMAT,
  NODE_SIZE,
  SPLIT_STR,
  X6ShapeTypeEnum
} from '@pages/JobWorkflow/constants';
import {isNumber} from 'lodash';
import {nodeMap} from '../../../globalVar';
import {IJsonData, IJsonEdgeData, IJsonNodeData, IX6EdgeData, IX6Node, IX6NodeData} from '../type';
import {X6_LINE} from '../x6DefaultConfig';
import {autoLayoutFn} from './layout';
/** 处理 x6 数据 转为 json 数据
 * @param cells x6 数据
 * @returns json 数据
 *
 * @example
 * dealX6ToJson({cells: [
 *   {id: '1', shape: X6ShapeTypeEnum.TASK, data: {name: '任务1'}},
 *   {id: '2', shape: X6ShapeTypeEnum.OPERATOR, data: {name: '算子1'}},
 * ]}) // {taskDefinitionList: [{id: '1', name: '任务1'}, {id: '2', name: '算子1'}]}
 */
export const dealX6ToJson = ({cells}: {cells: IX6Node[] | IX6EdgeData[]}): string => {
  const x6Nodes: IX6Node[] = cells.filter((cell) => cell.shape !== X6ShapeTypeEnum.EDGE) as IX6Node[];
  const x6Edges: IX6EdgeData[] = cells.filter((cell) => cell.shape === X6ShapeTypeEnum.EDGE) as IX6EdgeData[];

  const x6NodeMap = new Map<string, IX6Node>();
  x6Nodes.forEach((item) => {
    x6NodeMap.set(item.id, item);
  });
  console.log(cells);
  // 任务 边信息
  const taskRelationList: IJsonEdgeData[] = [];
  const taskDefinitionList: IJsonNodeData[] = [];
  // 算子 边信息
  const operatorRelationMap: Map<string, IJsonEdgeData[]> = new Map<string, IJsonEdgeData[]>();
  // 使用 x6 的 json 数据 获取连接信息和位置等信息
  x6Edges.forEach((x6Edge) => {
    // 边
    const source = x6Edge.source.cell;
    const target = x6Edge.target.cell;
    const node = x6NodeMap.get(source);
    // 算子节点
    if (node?.shape === X6ShapeTypeEnum.OPERATOR) {
      // 删除添加的 任务节点 Id
      const parent = node?.parent;
      const prefix = parent + SPLIT_STR;
      const operatorSource = source.slice(prefix.length);
      const operatorTarget = target.slice(prefix.length);

      if (operatorRelationMap.has(parent)) {
        operatorRelationMap.get(parent)?.push({source: operatorSource, target: operatorTarget});
      } else {
        operatorRelationMap.set(parent, [{source: operatorSource, target: operatorTarget}]);
      }
    } else {
      taskRelationList.push({source, target});
    }
  });
  x6Nodes.forEach((x6Node) => {
    // 节点参数配置
    const nodeParams = nodeMap.get(x6Node.id);

    // 节点 前端位置信息
    nodeParams.location = {
      position: x6Node.position,
      zIndex: x6Node.zIndex,
      visible: x6Node.visible,
      size: x6Node.size
    };
    // 任务节点 算子列表
    if ('type' in nodeParams && nodeParams.type === JobNodeTypeEnum.DATAFLOW_TASK) {
      // 过滤掉  children 里面还有连线 的 id
      nodeParams.operatorList =
        x6Node.children?.map((item) => nodeMap.get(item)).filter((item) => item) || [];
      nodeParams.operatorRelationList = operatorRelationMap.get(x6Node.id) || [];
    }

    if ('type' in nodeParams) {
      taskDefinitionList.push(nodeParams);
    }
  });

  return JSON.stringify(
    {
      taskDefinitionList,
      taskRelationList
    },
    null,
    JSON_FORMAT
  );
};

/**
 * 判断所有节点是否都配置了 position 信息
 * @param jsonData json 数据
 * @returns 是否都配置了 position 信息
 *
 * @example
 * isAllNodePositionConfig({taskDefinitionList: [{id: '1', name: '任务1'}, {id: '2', name: '算子1'}]}) // false
 */
const isAllNodePositionConfig = (jsonData: IJsonData): boolean => {
  let autoLayout = false;
  // 判断所有节点是否都配置了 position 信息
  for (const item of jsonData?.taskDefinitionList || []) {
    const position = item.location?.position;
    if (!position || !isNumber(position.x) || !isNumber(position.y)) {
      autoLayout = true;
      break;
    }

    for (const element of item.operatorList || []) {
      const position = element.location?.position;
      if (!position || !isNumber(position.x) || !isNumber(position.y)) {
        autoLayout = true;
        break;
      }
    }
    if (autoLayout) {
      break;
    }
  }
  return autoLayout;
};
/**
 * 处理后端 json 格式到 x6 数据
 * @param jsonData json 数据
 * @returns x6 数据
 *
 * @example
 * dealJsonToX6({taskDefinitionList: [{id: '1', name: '任务1'}, {id: '2', name: '算子1'}]}) // {cells: [
 *   {id: '1', shape: X6ShapeTypeEnum.TASK, data: {name: '任务1'}},
 *   {id: '2', shape: X6ShapeTypeEnum.OPERATOR, data: {name: '算子1'}},
 * ]})
 */
export const dealJsonToX6 = (jsonData: IJsonData): any => {
  // 是否自动布局
  const autoLayout = isAllNodePositionConfig(jsonData);

  const nodeIdSet: Set<string> = new Set<string>();

  // 任务节点
  const nodeArr: IX6Node[] = [];
  // 任务 算子节点
  const nodeOperatorArr: IX6Node[] = [];
  const edgeArr: IX6EdgeData[] = [];
  // 算子连线关系
  const edgeOperatorArr: IX6EdgeData[] = [];

  // 节点 zIndex 记录 任务节点 每层增加 10
  const zIndexMap = new Map<string, number>();
  let zIndex = 1;
  jsonData?.taskDefinitionList?.forEach((item) => {
    zIndexMap.set(item.id, zIndex++ * 10);
  });
  // 处理任务 连线
  jsonData?.taskRelationList?.forEach((item) => {
    edgeArr.push({
      zIndex: NODE_SIZE[X6ShapeTypeEnum.TASK_EDGE].zIndex,
      shape: X6ShapeTypeEnum.EDGE,
      source: {cell: item.source},
      target: {cell: item.target},
      attrs: X6_LINE
    });
  });
  // 处理 算子连线关系
  jsonData?.taskDefinitionList?.forEach((item) => {
    item.operatorRelationList?.forEach((operate) => {
      edgeOperatorArr.push({
        zIndex: zIndexMap.get(item.id) + NODE_SIZE[X6ShapeTypeEnum.OPERATOR_EDGE].zIndex,
        shape: X6ShapeTypeEnum.EDGE,
        source: {cell: item.id + SPLIT_STR + operate.source},
        target: {cell: item.id + SPLIT_STR + operate.target},
        attrs: X6_LINE
      });
    });
  });
  // 清空节点map
  nodeMap.clear();
  // 处理节点
  jsonData?.taskDefinitionList?.forEach((item) => {
    // 避免任务 id 重复
    if (nodeIdSet.has(item.id)) {
      return;
    }
    nodeIdSet.add(item.id);
    const operatorArr: IX6Node[] = [];
    // 处理算子
    if (item.operatorList) {
      item.operatorList?.forEach((operate) => {
        const operatorId = item.id + '-' + operate.id;
        // 避免算子 id 重复
        if (nodeIdSet.has(operatorId)) {
          return;
        }
        nodeIdSet.add(operatorId);
        operatorArr.push({
          id: operatorId,
          parent: item.id,
          shape: X6ShapeTypeEnum.OPERATOR,
          x: operate.location?.position?.x,
          y: operate.location?.position?.y,
          zIndex: zIndexMap.get(item.id) + NODE_SIZE[X6ShapeTypeEnum.OPERATOR].zIndex,
          size: {
            width: NODE_SIZE[X6ShapeTypeEnum.OPERATOR].width,
            height: NODE_SIZE[X6ShapeTypeEnum.OPERATOR].height
          },
          data: {
            name: operate.name,
            operatorId: operate.id,
            parentId: item.id,
            type: JobNodeTypeEnum.OPERATOR_NODE,
            taskStatus: item.location?.taskStatus,
            subTaskCode: item.location?.subTaskCode,
            operatorIds: item.location?.operatorIds
          }
        });

        // 记录算子
        nodeMap.set(operatorId, operate);
      });
    }

    nodeArr.push({
      id: item.id,
      shape: X6ShapeTypeEnum.TASK,
      children: operatorArr.map((item) => item.id),
      x: item.location?.position?.x,
      y: item.location?.position?.y,
      visible: item.location?.visible,
      zIndex: zIndexMap.get(item.id) + NODE_SIZE[X6ShapeTypeEnum.TASK].zIndex,
      size: {width: NODE_SIZE[X6ShapeTypeEnum.TASK].width, height: NODE_SIZE[X6ShapeTypeEnum.TASK].height},
      data: {
        type: item.type,
        name: item.name,
        taskStatus: item.location?.taskStatus,
        subTaskCode: item?.location?.subTaskCode
      }
    });
    nodeOperatorArr.push(...operatorArr);

    // 记录任务
    nodeMap.set(item.id, item);
  });

  let result = [
    ...nodeArr,
    ...nodeOperatorArr,
    ...edgeArr.filter((item) => nodeIdSet.has(item.source.cell) && nodeIdSet.has(item.target.cell)),
    ...edgeOperatorArr.filter((item) => nodeIdSet.has(item.source.cell) && nodeIdSet.has(item.target.cell))
  ];
  if (autoLayout) {
    // 处理 算子节点 布局
    result = autoLayoutFn(result);
  }

  // 处理 节点分组 若有结果
  if (nodeArr?.some((item) => item?.data?.taskStatus)) {
    // 处理 节点分组
    const nodeGroupArr = dealNodeGroup(result);
    const parentMap = new Map<string, string>();
    nodeGroupArr.forEach((item) => {
      parentMap.set(item.parent, item.id);
    });
    result.forEach((item: IX6Node) => {
      if (parentMap.has(item.id)) {
        item.children?.push(parentMap.get(item.id));
      }
    });

    result = [...result, ...nodeGroupArr];
  }

  return {
    cells: result
  };
};

/**
 * 处理 节点分组 覆盖在子节点上方
 * @param cells 节点
 * @returns 节点分组
 */
const dealNodeGroup = (cells: IX6Node[]) => {
  // 节点分组
  const nodeGroupObj: Record<
    string,
    {
      index: number;
      parent: string;
      subTaskCode: string;
      taskStatus: string;
      zIndex: number;
      minX: number;
      maxX: number;
      minY: number;
      maxY: number;
    }
  > = {};
  // 节点分组index
  const nodeGroupIndexObj: Record<string, number> = {};
  // 处理 节点分组大小
  cells.forEach((operatorNode: IX6Node) => {
    if (operatorNode.shape === X6ShapeTypeEnum.OPERATOR) {
      const data: IX6NodeData = operatorNode.data;
      const groupId = operatorNode.parent + SPLIT_STR + data?.subTaskCode;

      if (nodeGroupObj[groupId]) {
        nodeGroupObj[groupId].minX = Math.min(nodeGroupObj[groupId].minX, operatorNode?.x);
        nodeGroupObj[groupId].maxX = Math.max(nodeGroupObj[groupId].maxX, operatorNode?.x);
        nodeGroupObj[groupId].minY = Math.min(nodeGroupObj[groupId].minY, operatorNode?.y);
        nodeGroupObj[groupId].maxY = Math.max(nodeGroupObj[groupId].maxY, operatorNode?.y);
      } else {
        if (nodeGroupIndexObj[groupId]) {
          nodeGroupIndexObj[groupId] = nodeGroupIndexObj[groupId] + 1;
        } else {
          nodeGroupIndexObj[groupId] = 0;
        }
        nodeGroupObj[groupId] = {
          index: nodeGroupIndexObj[groupId],
          parent: data.parentId,
          subTaskCode: data?.subTaskCode,
          taskStatus: data?.taskStatus,
          zIndex: data.zIndex - 1,
          minX: operatorNode?.x,
          maxX: operatorNode?.x,
          minY: operatorNode?.y,
          maxY: operatorNode?.y
        };
      }
    }
  });

  return Object.keys(nodeGroupObj).map((groupId) => {
    const group = nodeGroupObj[groupId];
    const x = group.minX - NODE_SIZE[X6ShapeTypeEnum.OPERATOR_GROUP].padding;
    const y =
      group.minY -
      NODE_SIZE[X6ShapeTypeEnum.OPERATOR_GROUP].padding -
      NODE_SIZE[X6ShapeTypeEnum.OPERATOR_GROUP].titleHeight;
    const width =
      group.maxX -
      group.minX +
      NODE_SIZE[X6ShapeTypeEnum.OPERATOR].width +
      NODE_SIZE[X6ShapeTypeEnum.OPERATOR_GROUP].padding * 2;
    const height =
      group.maxY -
      group.minY +
      NODE_SIZE[X6ShapeTypeEnum.OPERATOR].height +
      NODE_SIZE[X6ShapeTypeEnum.OPERATOR_GROUP].padding * 2 +
      NODE_SIZE[X6ShapeTypeEnum.OPERATOR_GROUP].titleHeight;
    return {
      id: groupId,
      parent: group.parent,
      shape: X6ShapeTypeEnum.OPERATOR_GROUP,
      x,
      y,
      size: {width, height},
      zIndex: group.zIndex,
      data: {
        ...group
      }
    };
  });
};

/**
 * 处理算子详情
 * @param operator 算子
 * @returns 算子详情
 */
export const dealOperatorDetail = (operator: IOperVersionOneRes) => {
  return [
    {
      label: '算子名称',
      value: operator.operatorAlias
    },
    {
      label: '算子描述',
      value: operator.comment
    },
    {
      label: '可运行引擎',
      value: operator.engineType.join(',')
    },
    {
      label: '算子标识',
      value: operator.operatorName
    },
    {
      label: '算子版本',
      value: operator.name
    }
  ];
};
