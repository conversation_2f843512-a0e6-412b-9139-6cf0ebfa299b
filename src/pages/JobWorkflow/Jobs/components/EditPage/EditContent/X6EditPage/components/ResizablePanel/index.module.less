/**
 * 可调整大小的面板样式
 * 用于实现可拖拽调整宽度的面板组件
 */

// 面板容器样式
.resizable-panel {
  position: relative; // 相对定位，作为拖拽手柄的定位参考
  flex: 0 0 auto; // flex布局，不允许伸缩，保持原始大小
  overflow: auto; // 内容溢出时显示滚动条
  border-right: 1px solid #e8e9eb; // 右侧边框
}

// 拖拽手柄样式
.resize-handle {
  position: absolute; // 绝对定位
  top: 0; // 顶部对齐
  right: 0; // 右侧对齐
  width: 4px; // 手柄宽度
  height: 100%; // 手柄高度占满容器
  cursor: col-resize; // 显示水平调整光标
  background-color: transparent; // 默认透明背景
  transition: background-color 0.2s; // 背景色过渡动画

  // 鼠标悬停效果
  &:hover {
    background-color: #2468f2; // 悬停时显示蓝色背景
  }
}
