/**
 * X6的 默认配置
 */

// 连接桩的默认样式
const X6_PORTS_CIRCLE = {
  magnet: true,
  stroke: '#fff',
  strokeWidth: 2,
  fill: '#8f8f8f',
  r: 5,
  style: {
    visibility: 'hidden'
  }
};

// 任务节点 的 连接桩
export const X6_PORTS_TASK = {
  groups: {
    left: {
      position: 'left',
      attrs: {
        circle: X6_PORTS_CIRCLE
      }
    },
    right: {
      position: 'right',
      attrs: {
        circle: X6_PORTS_CIRCLE
      }
    }
  },
  items: [
    {
      id: 'left',
      group: 'left'
    },
    {
      id: 'right',
      group: 'right'
    }
  ]
};

// 算子节点 的 连接桩
export const X6_PORTS_OPERATOR = {
  groups: {
    top: {
      position: 'top',
      attrs: {
        circle: X6_PORTS_CIRCLE
      }
    },
    bottom: {
      position: 'bottom',
      attrs: {
        circle: X6_PORTS_CIRCLE
      }
    }
  },
  items: [
    {
      id: 'top',
      group: 'top'
    },
    {
      id: 'bottom',
      group: 'bottom'
    }
  ]
};

// 连线的默认样式
export const X6_LINE = {
  line: {
    stroke: '#84868C',
    // sourceMarker: {
    //   tagName: 'circle',
    //   r: 4,
    //   cx: -2,
    //   fill: '#84868C',
    //   strokeWidth: 2,
    //   stroke: '#fff'
    // },
    targetMarker: {
      tagName: 'polyline',
      strokeLinejoin: 'round',
      fill: 'none',
      strokeLinecap: 'round',
      strokeWidth: '2',
      transform: 'scale(1, -1)',
      points: '-4.8206941 -3.70141 -0.83559818 0 -4.8206941 3.79698866',
      stroke: '#84868C'
    }
  }
};
