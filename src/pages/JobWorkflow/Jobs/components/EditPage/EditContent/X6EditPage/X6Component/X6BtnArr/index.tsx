import IconSvg from '@components/IconSvg';
import {X6BtnArrTypeEnum} from '@pages/JobWorkflow/constants';
import {Input, Tooltip} from 'acud';
import {useMemoizedFn} from 'ahooks';
import classNames from 'classnames';
import React, {useEffect, useState} from 'react';
import styles from './index.module.less';

// 最大放大倍数
const max = 500;
// 最小放大倍数
const min = 1;
// 默认放大倍数
const normal = 100;
// 处理字符串转换为数字
const dealStringToNumber = (numStr: string): number => {
  const match = numStr.match(/^(-?\d+(\.\d+)?)/); // 匹配开头的整数或小数
  return Number(match ? match[0] : normal);
};
// 处理最大最小值
const dealMaxMin = (numStr: string | number): number => {
  const num = dealStringToNumber(String(numStr));

  if (num > max) {
    return max;
  }
  if (num <= min) {
    return min;
  }
  return Math.round(num);
};
/**
 * 缩放组件
 * @param props 组件参数
 */
const ZoomComponentFn = (props: {
  onChange: (value: number) => void;
  value: number;
  clickFn: (value: X6BtnArrTypeEnum) => void;
}) => {
  const [value, setValue] = useState<string>();

  // 组件值变化
  useEffect(() => {
    setValue(dealMaxMin(props.value) + '%');
  }, [props.value]);

  // 处理值加百分号
  const dealValue = (num: string | number) => {
    return num + '%';
  };

  // 输入框内容变化
  const onChange = useMemoizedFn((e: React.ChangeEvent<HTMLInputElement>) => {
    setValue(e.target.value);
  });

  // 失去焦点 处理输入框内容
  const onBlur = useMemoizedFn(() => {
    const num = dealMaxMin(value);
    props.onChange(num);
    setValue(dealValue(num));
  });

  // 点击放大缩小图标
  const onClickIcon = useMemoizedFn((num: number) => {
    const newNum = dealMaxMin(String(dealStringToNumber(value) + num));
    setValue(dealValue(newNum));
    props.onChange(newNum);
  });

  return (
    <Input
      size="large"
      className={styles['zoom-input']}
      prefix={
        <>
          <Tooltip title="自动对齐">
            <IconSvg
              className={classNames(styles['btn-icon'], styles['margin-right'])}
              onClick={() => props.clickFn(X6BtnArrTypeEnum.AUTO_LAYOUT)}
              type="workflow-auto-layout"
              size={24}
            />
          </Tooltip>

          <Tooltip title="自动定位">
            <IconSvg
              className={classNames(styles['btn-icon'], styles['margin-right'])}
              onClick={() => props.clickFn(X6BtnArrTypeEnum.FOCUS_CENTER)}
              type="workflow-focus-center"
              size={24}
            />
          </Tooltip>

          <span className={styles['item-split']}></span>
          <Tooltip title="缩小">
            <IconSvg
              className={styles['btn-icon']}
              onClick={() => onClickIcon(-5)}
              type="workflow-zoom-in"
              size={16}
            />
          </Tooltip>
        </>
      }
      suffix={
        <Tooltip title="放大">
          <IconSvg
            className={styles['btn-icon']}
            onClick={() => onClickIcon(5)}
            type="workflow-zoom-out"
            size={16}
          />
        </Tooltip>
      }
      value={value}
      onBlur={onBlur}
      onChange={onChange}
    />
  );
};

export const X6BtnArr = React.memo(ZoomComponentFn);
