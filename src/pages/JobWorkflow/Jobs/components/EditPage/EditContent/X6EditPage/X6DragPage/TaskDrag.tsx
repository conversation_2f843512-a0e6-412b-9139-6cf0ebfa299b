import {Graph} from '@antv/x6';
import React from 'react';

import {Dnd} from '@antv/x6-plugin-dnd';

import IconSvg from '@components/IconSvg';
import {JobNodeTypeEnum, JobTaskType, NODE_SIZE, X6ShapeTypeEnum} from '@pages/JobWorkflow/constants';
import {getNewId, getNewTaskName} from '../tools/copy';
import {handleMouseDown} from './clickFn';
import styles from './index.module.less';

//定义类型
interface IX6DragProps {
  graph?: React.RefObject<Graph>;
  dnd?: React.RefObject<Dnd>;
  ref?: React.RefObject<HTMLDivElement>;
}

const INIT_TASK_PARAM = {
  [JobNodeTypeEnum.DATAFLOW_TASK]: {
    parallel: 5,
    clusterList: [
      {
        engineType: 'RAY',
        clusterType: 'RESIDENT'
      }
    ]
  },
  [JobNodeTypeEnum.RAY_TASK]: {
    codePath: '',
    entryPoint: '',
    envVars: [{key: '', value: ''}],
    clusterList: [{engineType: 'RAY', clusterType: 'RESIDENT'}]
  }
};
// 任务 拖拽组件
const TaskDrag: React.FC<IX6DragProps> = ({graph, dnd, ref}) => {
  // 拖拽组件节点
  const startDrag = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    const target = e.currentTarget;
    const type: JobNodeTypeEnum = target.getAttribute('data-type') as JobNodeTypeEnum;
    const id = getNewId();
    const name = getNewTaskName(type);
    // 初始化数据
    const node = graph?.current?.createNode({
      id,
      shape: X6ShapeTypeEnum.TASK,
      data: {
        name,
        type,
        params: {
          id,
          name,
          type,
          description: '',
          taskParam: INIT_TASK_PARAM[type]
        }
      },
      width: NODE_SIZE[X6ShapeTypeEnum.TASK].width,
      height: NODE_SIZE[X6ShapeTypeEnum.TASK].height
    });

    handleMouseDown(e, node);
    dnd?.current?.start(node, e.nativeEvent as any);
  };

  return (
    <div className={styles['dnd-content']} ref={ref}>
      <div
        data-type={JobTaskType[JobNodeTypeEnum.DATAFLOW_TASK].value}
        className={styles['node']}
        onMouseDown={startDrag}
        onDragEnd={(e) => {
          console.log(e);
        }}
      >
        <span className={styles['node-icon']}>
          <IconSvg size={20} type={JobTaskType[JobNodeTypeEnum.DATAFLOW_TASK].icon} />
        </span>
        <span className={styles['node-text']}>{JobTaskType[JobNodeTypeEnum.DATAFLOW_TASK].label}</span>
      </div>
      <div
        data-type={JobTaskType[JobNodeTypeEnum.RAY_TASK].value}
        className={styles['node']}
        onMouseDown={startDrag}
      >
        <span className={styles['node-icon']}>
          <IconSvg size={20} type={JobTaskType[JobNodeTypeEnum.RAY_TASK].icon} />
        </span>
        <span className={styles['node-text']}>{JobTaskType[JobNodeTypeEnum.RAY_TASK].label}</span>
      </div>
    </div>
  );
};

export default React.memo(TaskDrag);
