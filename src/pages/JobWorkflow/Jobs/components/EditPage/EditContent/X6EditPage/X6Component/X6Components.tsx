/**
 * 任务节点组件
 *
 * 小地图展示组件效果
 * 注册的 task 和 operator 组件
 * 缩放组件
 */
import {EdgeView, Node, NodeView, Point} from '@antv/x6';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import {JobInstanceTaskStatusMap} from '@pages/JobInstance/constants';
import {IAppState} from '@store/index';
import React from 'react';
import {useSelector} from 'react-redux';
// task小地图展示效果
export class SimpleNodeTaskView extends NodeView {
  protected renderMarkup() {
    return this.renderJSONMarkup({
      tagName: 'rect',
      selector: 'body'
    });
  }

  update() {
    super.update({
      body: {
        refWidth: '100%',
        refHeight: '100%',
        fill: '#E8E9EB'
      }
    });
  }
}

// 算子节点展示效果
export class SimpleNodeOperatorView extends NodeView {
  protected renderMarkup() {
    return this.renderJSONMarkup({
      tagName: 'rect',
      selector: 'body'
    });
  }

  update() {
    super.update({
      body: {
        refWidth: '100%',
        refHeight: '100%',
        fill: '#fff'
      }
    });
  }
}

/**
 * 算子组节点组件
 */
export const OperatorGroupNodeComponent = ({node}: {node: Node}): React.ReactNode => {
  const {name, taskStatus, subTaskCode} = node.getData();
  // 当前选中的节点ID
  const selectedSubTaskCode = useSelector((state: IAppState) => state.workflowSlice.subTaskCode);

  return (
    <div
      className={`x6-component-operator-group-node `}
      style={{borderColor: JobInstanceTaskStatusMap[taskStatus]?.borderColor || 'transparent'}}
    >
      <div className={`${selectedSubTaskCode === subTaskCode ? 'selected' : ''}`}>
        <div className="operator-group-node-name">
          <div className="operator-group-node-name-text">
            <Ellipsis tooltip={name}>{name} </Ellipsis>
          </div>
          {taskStatus && (
            <div className="operator-group-node-status">
              <span
                className="circle"
                style={{backgroundColor: JobInstanceTaskStatusMap[taskStatus].color}}
              ></span>
              {JobInstanceTaskStatusMap[taskStatus].label}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export function autoRouter(vertices: Point.PointLike[], args: any, view: EdgeView) {
  // const points = vertices.map((p) => Point.create(p));
  const points = [];

  const sourceCorner = view.sourceBBox.getRightMiddle();
  const targetCorner = view.targetBBox.getLeftMiddle();

  // 控制点偏移量（越大弯曲越明显）
  const offset = (targetCorner.x - sourceCorner.x) / 2;

  points.push(Point.create(sourceCorner.x, sourceCorner.y));
  points.push(Point.create(sourceCorner.x + offset, sourceCorner.y));
  // points.push(Point.create(targetCorner.x - offset, targetCorner.y));

  points.push(Point.create(targetCorner.x, targetCorner.y));

  console.log(points);
  return points;
}
