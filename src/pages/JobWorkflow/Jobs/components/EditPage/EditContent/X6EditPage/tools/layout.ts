/**
 * 处理 x6 数据
 *
 * 将 json 数据格式 转为 x6 数据格式
 * 将 x6 数据格式 转为 json 数据格式
 */

import {DagreLayout} from '@antv/layout';
import {Cell, Graph, Node} from '@antv/x6';
import {NODE_SIZE, X6ShapeTypeEnum} from '@pages/JobWorkflow/constants';
import {IX6EdgeData, IX6Node} from '../type';

export const autoLayoutFn = (cells: IX6Node[] | IX6EdgeData[]): IX6Node[] | IX6EdgeData[] => {
  const taskDagreLayout = new DagreLayout({
    type: 'dagre',
    // LR 表示 从左到右（Left to Right）常见取值：TB（上到下）、BT（下到上）、LR、RL
    rankdir: 'LR',
    // 对齐方式（可选），UL 表示 左上对齐。可选：UL, UR, DL, DR
    align: 'DL',
    // 不同“层”之间的间距（横向间距，若 LR 就是 X 方向）
    ranksep: NODE_SIZE[X6ShapeTypeEnum.TASK].ranksep,
    // 同一层节点之间的间距（纵向间距，若 LR 就是 Y 方向）
    nodesep: NODE_SIZE[X6ShapeTypeEnum.TASK].nodesep
    // nodeSize: [NODE_SIZE[X6ShapeTypeEnum.OPERATOR].width, NODE_SIZE[X6ShapeTypeEnum.OPERATOR].height]
  });

  // 任务节点
  const taskNodeArr: IX6Node[] = [];
  const edgeArr: IX6EdgeData[] = [];
  const taskSizeMap = new Map<string, {width: number; height: number}>();

  const taskMap = new Map<string, IX6Node>();
  // 算子节点
  const operatorMap = new Map<string, IX6Node>();
  // 算子节点布局结果(需要二次偏移)
  const operatorResultMap = new Map<string, IX6Node[]>();
  cells.forEach((item) => {
    if (item.shape === X6ShapeTypeEnum.TASK) {
      taskNodeArr.push(item);
      taskMap.set(item.id, item);
    } else if (item.shape === X6ShapeTypeEnum.OPERATOR) {
      // 算子节点 设置为可见 不然布局子节点宽高会计算错误
      item.visible = true;
      operatorMap.set(item.id, item);
    } else if (item.shape === X6ShapeTypeEnum.EDGE) {
      edgeArr.push(item);
    }
  });

  const resultNode: IX6Node[] = [];
  const resultEdge: any[] = [];

  // 先处理 算子节点 计算出 算子任务的 节点大小
  taskNodeArr.forEach((node: IX6Node) => {
    if (node.children && node.children.length > 0) {
      const operatorDagreLayout = new DagreLayout({
        type: 'dagre',
        rankdir: 'TB',
        align: 'UR',
        ranksep: NODE_SIZE[X6ShapeTypeEnum.OPERATOR].ranksep,
        nodesep: NODE_SIZE[X6ShapeTypeEnum.OPERATOR].nodesep,
        begin: [NODE_SIZE[X6ShapeTypeEnum.TASK].padding.left, NODE_SIZE[X6ShapeTypeEnum.TASK].padding.top]
      });

      // 算子节点ID
      const operatorIdArr: string[] = node.children.filter((id) => operatorMap.has(id));
      const set = new Set(operatorIdArr);
      const operatorNodeArr: IX6Node[] = operatorIdArr.map((id) => operatorMap.get(id));
      const operatorResult = operatorDagreLayout.layout({
        nodes: operatorNodeArr,
        edges: edgeArr.filter((item) => set.has(item.source.cell) && set.has(item.target.cell))
      } as any);

      const maxX = Math.max(...operatorResult.nodes.map((item: IX6Node) => item.x));
      const maxY = Math.max(...operatorResult.nodes.map((item: IX6Node) => item.y));
      operatorResultMap.set(node.id, operatorResult.nodes);
      taskSizeMap.set(node.id, {
        width:
          maxX + NODE_SIZE[X6ShapeTypeEnum.OPERATOR].width + NODE_SIZE[X6ShapeTypeEnum.TASK].padding.right,
        height:
          maxY + NODE_SIZE[X6ShapeTypeEnum.OPERATOR].height + NODE_SIZE[X6ShapeTypeEnum.TASK].padding.bottom
      });
      resultEdge.push(...operatorResult.edges);
    }
  });
  // 计算 任务节点大小
  taskNodeArr.forEach((node: IX6Node) => {
    if (taskSizeMap.has(node.id) && node?.data?.isExpanded) {
      node.size = taskSizeMap.get(node.id);
      console.log(taskSizeMap.get(node.id));
    } else {
      taskSizeMap.set(node.id, {
        width: NODE_SIZE[X6ShapeTypeEnum.TASK].width,
        height: NODE_SIZE[X6ShapeTypeEnum.TASK].height
      });
    }
  });
  console.log(taskNodeArr);

  // 任务节点布局
  const taskResult = taskDagreLayout.layout({
    nodes: taskNodeArr,
    edges: edgeArr.filter((item) => taskMap.has(item.source.cell) && taskMap.has(item.target.cell))
  } as any);

  resultNode.push(...taskResult.nodes);
  resultEdge.push(...taskResult.edges);

  taskResult.nodes.forEach((node: IX6Node) => {
    node.x = node.x - taskSizeMap.get(node.id)?.width / 2;
    node.y = node.y - taskSizeMap.get(node.id)?.height / 2;
  });
  // 二次偏移 算子节点
  taskResult.nodes.forEach((node: IX6Node) => {
    if (operatorResultMap.has(node.id)) {
      const operatorResult = operatorResultMap.get(node.id);
      operatorResult.forEach((item: IX6Node) => {
        item.x = item.x + node.x;
        item.y = item.y + node.y;
      });
      resultNode.push(...operatorResult);
    }
  });

  return [...resultNode, ...resultEdge];
};

// 自动计算 父节点大小
export const autoParentSize = (parent: Node) => {
  if (parent && parent.isNode()) {
    const children = parent.getChildren().filter((child) => child?.isNode());
    if (children && children.length > 0) {
      const position = parent.getPosition();
      const x = position.x;
      const y = position.y;
      const cornerX = position.x + parent.getSize().width;
      const cornerY = position.y + parent.getSize().height;
      console.log(x, y, cornerX, cornerY);

      let minX = Infinity;
      let minY = Infinity;
      let maxX = -Infinity;
      let maxY = -Infinity;
      children.forEach((child) => {
        if (child.isNode()) {
          const positionChildren = child.getPosition();
          minX = Math.min(minX, positionChildren.x);
          minY = Math.min(minY, positionChildren.y);
          maxX = Math.max(maxX, positionChildren.x);
          maxY = Math.max(maxY, positionChildren.y);
        }
      });
      const width =
        maxX -
        minX +
        NODE_SIZE[X6ShapeTypeEnum.OPERATOR].width +
        NODE_SIZE[X6ShapeTypeEnum.TASK].padding.left +
        NODE_SIZE[X6ShapeTypeEnum.TASK].padding.right;
      const height =
        maxY -
        minY +
        NODE_SIZE[X6ShapeTypeEnum.OPERATOR].height +
        NODE_SIZE[X6ShapeTypeEnum.TASK].padding.top +
        NODE_SIZE[X6ShapeTypeEnum.TASK].padding.bottom;

      const positionX = minX - NODE_SIZE[X6ShapeTypeEnum.TASK].padding.left;
      const positionY = minY - NODE_SIZE[X6ShapeTypeEnum.TASK].padding.top;

      parent.prop({
        position: {x: positionX, y: positionY},
        size: {width, height}
      });
    }
  }
};

export const dealZIndex = (cell: Cell, graph: Graph) => {
  // 获取父节点
  let parent = cell.getParent();
  if (!parent) {
    parent = cell;
  }
  parent.toFront();
  if (parent.shape === X6ShapeTypeEnum.TASK && parent.children?.length > 0) {
    const children = parent.children.filter((child) => child.isNode());
    // 连线置顶
    children.forEach((child) => {
      graph.getOutgoingEdges(child)?.forEach((edge) => {
        edge.toFront();
      });
    });
    // 节点置顶
    children.forEach((child) => {
      child.toFront();
    });
  }
};
