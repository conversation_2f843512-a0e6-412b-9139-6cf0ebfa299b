/**
 * 任务节点组件
 *
 * 小地图展示组件效果
 * 注册的 task 和 operator 组件
 * 缩放组件
 */
import {Node} from '@antv/x6';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import IconSvg from '@components/IconSvg';
import {JobInstanceTaskStatusMap} from '@pages/JobInstance/constants';
import {
  JobNodeTypeEnum,
  NODE_SIZE,
  SelectJobNodeTypeEnum,
  X6ShapeTypeEnum
} from '@pages/JobWorkflow/constants';
import {IAppState} from '@store/index';
import {useMemoizedFn} from 'ahooks';
import React, {useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

/**
 * 任务节点组件
 * @param node 传递节点参数
 */
export const TaskNodeComponent = ({node}: {node: Node}): React.ReactNode => {
  // 是否悬停
  const [showTool, setShowTool] = useState(false);
  const [hovered, setHovered] = useState(false);
  // 算子父节点 id 若有值 在算子编辑界面
  const operateParentId = useSelector((state: IAppState) => state.workflowSlice.operateParentId);
  // 当前选中的节点ID
  const selectedNodeId = useSelector((state: IAppState) => state.workflowSlice.selectedNodeId);
  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);

  // name 名称 ,type 类型 ,isExpanded 是否展开 ,taskStatus 状态(结果)  disabled 是否禁用
  const {name, type, isExpanded, taskStatus} = node.getData();

  useEffect(() => {
    // 算子编辑界面
    if (operateParentId) {
      setShowTool(node.id === operateParentId);
    } else if (isEditing && selectedNodeId === node.id) {
      setShowTool(true);
    } else {
      setShowTool(false);
    }
  }, [operateParentId, isEditing, selectedNodeId, node.id]);
  //  处理子节点
  const dealChildrenNode = useMemoizedFn(() => {
    const cells = node.getChildren()?.filter((child) => child?.isNode());
    if (cells) {
      cells.forEach((cell) => {
        if (isExpanded) {
          cell.show();
        } else {
          cell.hide();
        }
      });
    }
  });
  // 折叠
  const changeCollapsed = useMemoizedFn(() => {
    if (isExpanded) {
      node.fit({padding: NODE_SIZE[X6ShapeTypeEnum.TASK].padding});
      const bbox = node.getBBox(); // 获取当前的包围盒信息
      const width = Math.max(bbox.width, NODE_SIZE[X6ShapeTypeEnum.TASK].width);
      const height = Math.max(bbox.height, NODE_SIZE[X6ShapeTypeEnum.TASK].height + 100);

      node.resize(width, height);
    } else {
      node?.resize(NODE_SIZE[X6ShapeTypeEnum.TASK].width, NODE_SIZE[X6ShapeTypeEnum.TASK].height);
    }
    dealChildrenNode();
  });

  useEffect(() => {
    changeCollapsed();
  }, [isExpanded]);

  useEffect(() => {
    dealChildrenNode();
  }, []);

  // 处理 鼠标悬停
  const handleMouseEnter = useMemoizedFn(() => {
    // 不是编辑当前节点  或者 非编辑状态  不展示
    if ((operateParentId && operateParentId !== node.id) || !isEditing) {
      return;
    }
    setHovered(true);
  });
  const handleMouseLeave = useMemoizedFn(() => {
    setHovered(false);
  });

  // 点击折叠
  const handleClickCollapse = useMemoizedFn(() => {
    node.setData({
      isExpanded: !isExpanded
    });
  });
  return (
    <div
      data-btn-type=""
      style={{width: '100%', height: '100%'}}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* 工具栏 */}
      {isEditing && (
        <div className={`x6-component-task-node-tool`} style={{opacity: showTool || hovered ? '1' : '0'}}>
          {(showTool || hovered) && (
            <div>
              <IconSvg className="btn-tool" data-btn-type={SelectJobNodeTypeEnum.COPY} type="copy" />
              <IconSvg className="btn-tool" data-btn-type={SelectJobNodeTypeEnum.DELETE} type="delete" />
            </div>
          )}
        </div>
      )}

      {/* 任务节点 */}
      <div
        style={{borderColor: JobInstanceTaskStatusMap[taskStatus]?.borderColor || 'transparent'}}
        className={'x6-component-task-node ' + (JobInstanceTaskStatusMap[taskStatus] ? 'status ' : ' ')}
      >
        <div className={'selected-border ' + (selectedNodeId === node.id ? 'selected ' : ' ')}>
          <div className={`task-node-title ${type}`}>
            {/* 折叠 只有算子组件 有 */}
            {type === JobNodeTypeEnum.DATAFLOW_TASK && (
              <IconSvg
                className="task-node-collapse-icon"
                size={16}
                type={isExpanded ? 'arrow-down' : 'arrow-right'}
                onClick={handleClickCollapse}
              />
            )}

            {/* 任务类型 */}
            <IconSvg
              className="task-node-title-icon"
              size={24}
              type={
                type === JobNodeTypeEnum.DATAFLOW_TASK
                  ? 'workflow-type-dataflow-task'
                  : 'workflow-type-ray-task'
              }
            />
            <div className="task-node-title-name">
              <Ellipsis tooltip={name}>{name}</Ellipsis>
            </div>
            {/* 结果 */}
            {taskStatus && (
              <div className="task-node-status">
                <span
                  className="circle"
                  style={{backgroundColor: JobInstanceTaskStatusMap[taskStatus].color}}
                ></span>
                {JobInstanceTaskStatusMap[taskStatus].label}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
