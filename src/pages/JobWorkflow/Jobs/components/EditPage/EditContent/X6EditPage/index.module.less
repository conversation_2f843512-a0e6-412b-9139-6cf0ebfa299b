.x6-main {
  position: relative;
  display: flex;
  flex: 1 1 auto;
  height: 100%;
  width: 100%;
  overflow: hidden;

  :global {
    // fix拖动宽度 抖动
    .x6-graph-scroller {
      overflow: hidden;
    }
  }
  .x6-drag-page {
    flex: 0 0 160px;
    border-right: 1px solid #e8e9eb;
    overflow: auto;
  }
  .x6-content {
    flex: 1 1 auto;
    overflow: hidden;
    position: relative;

    // 缩略图
    .minimap-container {
      position: absolute;
      bottom: 16px;
      left: 16px;
      width: 132px;
      height: 84px;
      z-index: 100;
      :global {
        .x6-widget-minimap {
          // border: 1px solid #ddd;
          border-radius: 8px;
          .x6-graph {
            box-shadow: none;
          }
          .x6-widget-minimap-viewport {
            box-shadow: 0 0 0 9999px rgba(7, 12, 20, 0.1);
            border: none;
          }

          .x6-widget-minimap-viewport-zoom {
            border: none;
            display: none;
          }
        }
      }
    }

    // 缩放按钮
    .zoom-btn {
      width: 200px;
      z-index: 100;
      position: absolute;
      bottom: 16px;
      right: 50%;
      transform: translateX(50%);
    }

    // 处理全局样式 body 覆盖 svg body 样式
    :global {
      body {
        min-width: 10px;
      }
    }
  }
  &.readonly-x6 {
    .x6-content {
      .minimap-container {
        // 取消居中
        left: auto;
        right: 16px;
        transform: translateX(0);
        bottom: 70px;
      }
      .zoom-btn {
        right: 16px;
        transform: translateX(0);
      }
    }
  }
}

:global {
  .x6-widget-dnd body {
    min-width: 0px;
  }
  .x6-component-task-node-tool {
    position: absolute;
    top: -36px;
    right: 0;
    height: 32px;
    min-width: 60px;
    padding: 0 12px;
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 6px;
    border: 1px solid #e8e9eb;
    box-shadow: 0 1px 4px 1px rgba(7, 12, 20, 0.12);
    display: flex;
    align-items: center;
    justify-content: center;
    .btn-tool {
      font-size: 16px;
      padding: 4px;
      width: 24px;
      height: 24px;
      cursor: pointer;
      &:hover {
        background-color: #f0f0f0;
      }
    }
  }

  .x6-component-task-node {
    box-sizing: content-box;
    position: relative;
    overflow: hidden;
    height: 100%;
    width: 100%;
    display: inline-block;
    font-size: 14px;
    text-align: center;
    color: #151b26;
    flex-direction: column;
    border-radius: 12px;
    background: #ffffff;
    box-shadow: 0 1px 4px 1px rgba(7, 12, 20, 0.12);
    filter: drop-shadow(0 1px 4px 1px rgba(7, 12, 20, 0.12));
    &.task-node-edit-disabled {
      background-color: #f0f0f0;
      cursor: not-allowed;
    }

    // 偏移边框的距离
    &.status {
      margin-top: -3px;
      margin-left: -3px;
      border: 3px solid transparent;
    }

    .selected-border {
      border: 2px solid transparent;
      border-radius: 12px;
      height: 100%;
      &.selected {
        border-color: #007aff;
      }
    }

    .task-node-title {
      width: 100%;
      height: 40px;
      box-sizing: border-box;
      padding: 8px 8px;
      font-size: 14px;
      font-weight: bold;
      text-align: left;
      color: #151b26;
      overflow: hidden;
      display: flex;
      gap: 4px;

      .task-node-collapse-icon {
        flex: 0 0 16px;
        padding: 4px 0;
      }
      .task-node-title-icon {
        flex: 0 0 24px;
        padding-left: 4px;
      }
      .task-node-title-name {
        flex: 1 1 auto;
        overflow: hidden;
        line-height: 24px;
      }
      .task-node-status {
        flex: 0 0 48px;
        padding: 4px 0;
        color: #5c5f66;
        font-size: 12px;
      }
    }
  }
  // 圆点状态
  .circle {
    display: inline-block;
    margin-right: 4px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }
  .x6-component-operator-node {
    box-sizing: border-box;
    &.selected {
      border: 1px solid #007aff;
    }
    height: 100%;
    width: 100%;
    text-align: left;
    font-size: 12px;
    color: #151b26;
    border-radius: 8px;
    background: #f9f9fb;
    box-shadow: 0 1px 4px 1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e8e9eb;
    padding: 0 12px;
    &.operator-node-edit-disabled {
      background-color: #f0f0f0;
      cursor: not-allowed;
    }
    .operator-node-name {
      width: 100%;
      display: flex;
      align-items: center;
      // 垂直居中
      height: 30px;
      line-height: 24px;
      font-size: 12px;
      justify-content: space-between;
      .operator-node-name-text {
        overflow: hidden;
        flex: 1 1 auto;
        display: flex;
        align-items: center;
      }
      .operator-node-status {
        color: #5c5f66;
        text-align: right;
        flex: 0 0 48px;
      }
    }
  }

  // 算子组节点
  .x6-component-operator-group-node {
    border-radius: 12px;
    border: 3px solid transparent;

    height: 100%;
    width: 100%;
    background-color: transparent;
    .selected {
      height: 100%;
      width: 100%;
      border-radius: 8px;
      border: 1px dashed #007aff;
    }
    .operator-group-node-name {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .operator-group-node-name-text {
        overflow: hidden;
        flex: 1 1 auto;
        line-height: 24px;
      }
      .operator-group-node-status {
        flex: 0 0 48px;

        color: #5c5f66;
      }
    }
  }
}
