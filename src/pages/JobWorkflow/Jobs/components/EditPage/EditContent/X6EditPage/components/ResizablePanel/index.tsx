/**
 * 可调整大小的面板组件
 * 用于实现可拖拽调整宽度的面板，支持最小/最大宽度限制
 */
import {useMemoizedFn} from 'ahooks';
import React, {useEffect, useRef, useState} from 'react';
import styles from './index.module.less';

interface ResizablePanelProps {
  /** 子元素 */
  children: React.ReactNode;
  /** 默认宽度 */
  defaultWidth?: number;
  /** 最小宽度 */
  minWidth?: number;
  /** 最大宽度 */
  maxWidth?: number;
  /** 自定义类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
}

const ResizablePanel: React.FC<ResizablePanelProps> = ({
  children,
  defaultWidth = 160,
  minWidth = 160,
  maxWidth = 300,
  className = '',
  style = {}
}) => {
  // 面板宽度状态
  const [width, setWidth] = useState(defaultWidth);
  // 是否正在拖拽
  const [isDragging, setIsDragging] = useState(false);
  // 面板DOM引用
  const panelRef = useRef<HTMLDivElement>(null);

  /**
   * 处理拖拽开始
   * 添加全局鼠标移动和抬起事件监听
   */
  const handleDragStart = useMemoizedFn((e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
    document.addEventListener('mousemove', handleDragMove);
    document.addEventListener('mouseup', handleDragEnd);
  });

  /**
   * 处理拖拽移动
   * 根据鼠标位置计算新的面板宽度
   */
  const handleDragMove = useMemoizedFn((e: MouseEvent) => {
    if (!isDragging || !panelRef.current) return;
    const container = panelRef.current.parentElement;
    if (!container) return;

    const containerRect = container.getBoundingClientRect();
    const newWidth = e.clientX - containerRect.left;

    // 限制宽度在最小值和最大值之间
    if (newWidth >= minWidth && newWidth <= maxWidth) {
      setWidth(newWidth);
    }
  });

  /**
   * 处理拖拽结束
   * 移除全局事件监听
   */
  const handleDragEnd = useMemoizedFn(() => {
    setIsDragging(false);
    document.removeEventListener('mousemove', handleDragMove);
    document.removeEventListener('mouseup', handleDragEnd);
  });

  // 组件卸载时清理事件监听
  useEffect(() => {
    return () => {
      document.removeEventListener('mousemove', handleDragMove);
      document.removeEventListener('mouseup', handleDragEnd);
    };
  }, []);

  return (
    <div
      ref={panelRef}
      className={`${styles['resizable-panel']} ${className}`}
      style={{
        ...style,
        flexBasis: `${width}px` // 使用flexBasis控制面板宽度
      }}
    >
      {children}
      {/* 拖拽手柄 */}
      <div className={styles['resize-handle']} onMouseDown={handleDragStart} />
    </div>
  );
};

export default ResizablePanel;
