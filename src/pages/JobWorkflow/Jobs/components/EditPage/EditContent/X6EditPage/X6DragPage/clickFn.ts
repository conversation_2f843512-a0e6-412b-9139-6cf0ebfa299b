import {Node} from '@antv/x6';
import {NODE_SIZE, X6ShapeTypeEnum} from '@pages/JobWorkflow/constants';
import React from 'react';
import {getGraph} from '../../../globalVar';

// 添加节点
const addNode = (node: Node) => {
  const graph = getGraph();

  let x = -Infinity;
  let y = -Infinity;

  // 算子节点大小
  const operatorSize = NODE_SIZE[X6ShapeTypeEnum.OPERATOR];
  // 任务节点大小
  const taskSize = NODE_SIZE[X6ShapeTypeEnum.TASK];

  const data = node?.getData();
  const parentId = data?.parentId;
  const parentNode = graph?.getCellById(parentId);
  // 添加算子节点
  if (node.shape === X6ShapeTypeEnum.OPERATOR) {
    if (parentNode) {
      x = (parentNode as Node).getPosition().x + taskSize.padding.left;
      y = (parentNode as Node).getPosition().y + taskSize.padding.top;

      const children = parentNode.getChildren();
      children
        ?.filter((item) => item.shape === X6ShapeTypeEnum.OPERATOR)
        .forEach((item) => {
          x = Math.max(x, (item as Node).getPosition().x);
          y = Math.max(y, (item as Node).getPosition().y);
        });
      y = y + operatorSize.height + operatorSize.ranksep + 20;
    }
  } else {
    // 添加任务节点
    graph.getNodes().forEach((item) => {
      if (item.shape !== X6ShapeTypeEnum.OPERATOR) {
        x = Math.max(x, item.getPosition().x);
        y = Math.max(y, item.getPosition().y);
      }
    });
    if (graph.getNodes().length > 0) {
      x = x + taskSize.width + taskSize.nodesep;
    } else {
      x = 0;
      y = 0;
    }
  }
  node.setPosition(x, y);
  const newNode = graph?.addNode(node);
  // 添加到父节点 避免重复添加
  if (parentNode) {
    parentNode.addChild(newNode);
  }
  graph?.centerCell(node);
};
/**
 * 处理鼠标按下事件 判断是否是点击事件 若是点击事件 则执行添加逻辑
 * @param e 鼠标事件
 * @param node 节点
 */
export const handleMouseDown = (e: React.MouseEvent, node: Node) => {
  let isDragging = false;
  let startX = 0;
  let startY = 0;
  isDragging = false;
  startX = e.clientX;
  startY = e.clientY;

  // 处理鼠标移动事件
  const handleMouseMove = (moveEvent: MouseEvent) => {
    const dx = moveEvent.clientX - startX;
    const dy = moveEvent.clientY - startY;
    if (Math.abs(dx) > 3 || Math.abs(dy) > 3) {
      isDragging = true;
    }
  };

  // 处理鼠标抬起事件
  const handleMouseUp = (upEvent: MouseEvent) => {
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);

    if (!isDragging) {
      addNode(node);
    }
  };

  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
};
