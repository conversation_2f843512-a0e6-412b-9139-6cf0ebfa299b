/**
 * 复制节点
 * getNewId 获取新的 id
 * getNewTaskName 获取新的任务名称
 * getNewOperatorName 获取新的算子名称
 * copyChildren 复制子节点和连接关系
 * copyNode 复制节点
 */

import {Cell, Edge, Graph, Node} from '@antv/x6';
import {
  JobNodeTypeEnum,
  JobTaskType,
  NODE_SIZE,
  SPLIT_STR,
  X6ShapeTypeEnum
} from '@pages/JobWorkflow/constants';
import {cloneDeep} from 'lodash';
import {getGraph, nodeMap} from '../../../globalVar';
import {IX6EdgeData, IX6Node} from '../type';

const nodeMapPrefixName = '未命名-';
// 后缀
const copySuffix = '-副本';
// 获取新的不重复 id
// 算子 id 使用 父节点 id + 算子 id 索引
export const getNewId = (parentId: string = '') => {
  const prefixId = parentId ? parentId + SPLIT_STR + 'opid-' : 'tid-';
  let idIndex = 0;
  let nodeId = prefixId + idIndex;
  while (nodeMap.has(nodeId)) {
    idIndex++;
    nodeId = prefixId + idIndex;
  }
  return nodeId;
};

// 获取新的不重复名称,可以传入复制的名称
export const getNewTaskName = (type: JobNodeTypeEnum, copyName?: string) => {
  const nameSet = new Set<string>();
  nodeMap.forEach((value) => {
    if ('type' in value && value.type === type) {
      nameSet.add(value.name);
    }
  });
  let name = nodeMapPrefixName + JobTaskType[type].label + SPLIT_STR;
  // 处理复制
  if (copyName) {
    name = copyName + copySuffix;
    if (!nameSet.has(name)) {
      return name;
    }
    name = copyName + copySuffix + SPLIT_STR;
  }

  let nameIndex = 0;
  while (nameSet.has(name + nameIndex)) {
    nameIndex++;
  }
  return name + nameIndex;
};

// 获取新的算子名称
export const getNewOperatorName = (operatorName: string, pId: string, copyName?: string) => {
  let nameIndex = 0;
  const graph = getGraph();

  // 任务名称 避免重复
  const nameSet = new Set<string>();
  let name = nodeMapPrefixName + operatorName + SPLIT_STR;

  // 算子名称 避免重复
  graph
    .getCellById(pId)
    ?.getChildren()
    ?.filter((item) => item.isNode())
    ?.forEach((item) => {
      nameSet.add(item.getData()?.name);
    });

  // 处理复制
  if (copyName) {
    name = copyName + copySuffix;
    if (!nameSet.has(name)) {
      return name;
    }
    name = copyName + copySuffix + SPLIT_STR;
  }
  while (nameSet.has(name + nameIndex)) {
    nameIndex++;
  }
  return name + nameIndex;
};

// 复制子节点和连接关系
const copyChildren = (node: Node, newParentId: string) => {
  const graph = getGraph();
  const children = node.getChildren()?.filter((child) => child?.isNode());

  // 旧 id 到新 id 的映射 主要用于关系连接
  const oldIdToNewId = new Map<string, string>();

  let idIndex = 0;
  const cells: IX6Node[] = [];
  const edges: IX6EdgeData[] = [];
  const nodeId = newParentId + SPLIT_STR + 'opid-';
  // 处理子节点 Id
  children.forEach((child: Cell) => {
    const childId = child.id;
    while (nodeMap.has(nodeId + idIndex)) {
      idIndex++;
    }
    const newId = nodeId + idIndex;
    oldIdToNewId.set(childId, newId);
    idIndex++;

    const nodeParams = nodeMap.get(childId);
    const childData = cloneDeep(nodeParams);
    childData.id = newId.slice(newParentId.length + 1);
    cells.push({
      id: newId,
      shape: X6ShapeTypeEnum.OPERATOR,
      data: {
        name: childData.name,
        parentId: newParentId,
        type: JobNodeTypeEnum.OPERATOR_NODE,
        params: childData
      },
      x: (child as Node).getPosition().x + NODE_SIZE[X6ShapeTypeEnum.OPERATOR].nodesep,
      y: (child as Node).getPosition().y + NODE_SIZE[X6ShapeTypeEnum.OPERATOR].ranksep
    });
  });
  children.forEach((child: Cell) => {
    graph.getOutgoingEdges(child)?.forEach((edge: Edge) => {
      edges.push({
        source: {
          cell: oldIdToNewId.get(edge.getSourceCellId())
        },
        target: {
          cell: oldIdToNewId.get(edge.getTargetCellId())
        }
      });
    });
  });

  return {cells, edges};
};

// 复制节点
export const copyNode = (node: Node, graph: Graph) => {
  const addCells: Cell[] = [];
  const nodeData = node.getData();

  const nodeParams = nodeMap.get(node.id);
  // 任务节点才有类型
  if ('type' in nodeParams && node.shape === X6ShapeTypeEnum.TASK) {
    const id = getNewId();
    const newNodeParams = cloneDeep(nodeParams);
    newNodeParams.id = id;
    newNodeParams.name = getNewTaskName(nodeParams.type, nodeParams.name);
    const newNode = graph.createNode({
      id,
      shape: X6ShapeTypeEnum.TASK,
      x: node.getPosition().x + NODE_SIZE[X6ShapeTypeEnum.TASK].nodesep,
      y: node.getPosition().y + NODE_SIZE[X6ShapeTypeEnum.TASK].ranksep,
      data: {
        name: newNodeParams.name,
        type: nodeParams.type,
        params: newNodeParams
      }
    });

    addCells.push(newNode);
    if (
      nodeParams.type === JobNodeTypeEnum.DATAFLOW_TASK &&
      node.getChildren()?.filter((child) => child?.isNode())?.length > 0
    ) {
      const {cells: childCells, edges: childEdges} = copyChildren(node, id);
      const childNodes = childCells.map((childCell: IX6Node) => graph.createNode(childCell as any));
      newNode?.setChildren(childNodes);
      childNodes.forEach((childNode) => {
        childNode.setParent(newNode);
      });

      addCells.push(...childNodes);
      addCells.push(
        ...childEdges.map((edge: IX6EdgeData) =>
          graph.createEdge({
            source: {
              cell: edge.source.cell
            },
            target: {
              cell: edge.target.cell
            }
          })
        )
      );
    }
  }

  if (node.shape === X6ShapeTypeEnum.OPERATOR) {
    const parentId = nodeData.parentId;

    const id = getNewId(parentId);

    const newNodeParams = cloneDeep(nodeParams);
    newNodeParams.id = id.slice(parentId.length + 1);
    newNodeParams.name = getNewOperatorName('', parentId, nodeParams.name);
    const newNode = graph.createNode({
      id,
      shape: X6ShapeTypeEnum.OPERATOR,
      data: {
        name: newNodeParams.name,
        parentId,
        type: JobNodeTypeEnum.OPERATOR_NODE,
        params: newNodeParams
      },
      x: node.getPosition().x + NODE_SIZE[X6ShapeTypeEnum.OPERATOR].nodesep,
      y: node.getPosition().y + NODE_SIZE[X6ShapeTypeEnum.OPERATOR].ranksep
    });

    addCells.push(newNode);
    graph.getCellById(parentId)?.addChild(newNode);
  }

  graph.addCell(addCells);
};
