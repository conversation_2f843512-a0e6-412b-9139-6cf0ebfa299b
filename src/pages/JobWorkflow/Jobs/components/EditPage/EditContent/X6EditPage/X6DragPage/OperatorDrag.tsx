import {Graph} from '@antv/x6';
import React, {useEffect, useState} from 'react';

import {Dnd} from '@antv/x6-plugin-dnd';

import {IOperVersionOneRes} from '@api/metaRequest';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import IconSvg from '@components/IconSvg';
import DescriptionList from '@pages/JobInstance/ResultPage/components/DescriptionList';
import {
  JobNodeTypeEnum,
  NODE_SIZE,
  OperatorCategoryChineseEnum,
  OperatorCategoryEnum,
  OperatorFieldChineseEnum,
  OperatorFieldEnum,
  SPLIT_STR,
  X6ShapeTypeEnum
} from '@pages/JobWorkflow/constants';
import {IAppState} from '@store/index';
import {Popover, Tree} from 'acud';
import {useMemoizedFn} from 'ahooks';
import {useSelector} from 'react-redux';
import {operatorDetailMap} from '../../../globalVar';
import {dealOperatorDetail, dealZIndex} from '../tools';
import {getNewId, getNewOperatorName} from '../tools/copy';
import {handleMouseDown} from './clickFn';
import styles from './index.module.less';

//定义类型
interface IX6DragProps {
  graph?: React.RefObject<Graph>;
  dnd?: React.RefObject<Dnd>;
  ref?: React.RefObject<HTMLDivElement>;
}

interface IOperatorTreeData {
  title: string | React.ReactNode;
  key: string;
  children?: IOperatorTreeData[];
}

// 算子拖拽组件
const OperatorDrag: React.FC<IX6DragProps> = ({graph, dnd, ref}) => {
  const operateParentId = useSelector((state: IAppState) => state.workflowSlice.operateParentId);

  const [treeData, setTreeData] = useState<IOperatorTreeData[]>([]);
  // 拖拽组件节点
  const startDrag = useMemoizedFn(
    (e: React.MouseEvent<HTMLDivElement, MouseEvent>, operator: IOperVersionOneRes) => {
      const id = getNewId(operateParentId);
      const name = getNewOperatorName(operator.operatorAlias, operateParentId);

      const cell = graph?.current.getCellById(operateParentId);
      cell?.setData({
        isExpanded: true
      });
      dealZIndex(cell, graph?.current);
      // 初始化数据
      const node = graph?.current?.createNode({
        id,
        shape: X6ShapeTypeEnum.OPERATOR,
        data: {
          name: name,
          parentId: operateParentId,
          type: JobNodeTypeEnum.OPERATOR_NODE,
          params: {
            name: name,
            id: id.slice(operateParentId.length + 1),
            params: operator.execParams.map((item) => {
              return {
                key: item.name,
                value: item.defaultValue,
                valueType: item.type
              };
            }),
            metaData: {
              catalogName: operator.catalogName,
              schemaName: operator.schemaName,
              operatorName: operator.operatorName,
              version: operator.name
            },
            location: {}
          }
        },
        width: NODE_SIZE[X6ShapeTypeEnum.OPERATOR].width,
        height: NODE_SIZE[X6ShapeTypeEnum.OPERATOR].height
      });

      handleMouseDown(e, node);

      dnd?.current?.start(node, e.nativeEvent as any);
    }
  );

  // 初始化算子详情
  const initOperatorDetail = async () => {
    const arr = Array.from(operatorDetailMap.values());

    const map = new Map<string, IOperVersionOneRes[]>();

    // 所有算子
    arr.forEach((item) => {
      const category = item.category || OperatorCategoryEnum.OTHERS;
      const field = item.field || OperatorFieldEnum.GENERAL;
      const mapKey = category + SPLIT_STR + field;
      // 算子类别 和 算子 过滤类型 作为 key 存储
      if (map.has(mapKey)) {
        map.get(mapKey).push(item);
      } else {
        map.set(mapKey, [item]);
      }
    });

    const treeArr: IOperatorTreeData[] = [];
    // 算子类别
    Object.entries(OperatorCategoryChineseEnum).forEach(([key, value]) => {
      const category: IOperatorTreeData = {
        title: value,
        key: key,
        children: []
      };
      // 算子  过滤类型
      Object.entries(OperatorFieldChineseEnum).forEach(([operatorFieldKey, operatorFieldValue]) => {
        const operatorKey = key + SPLIT_STR + operatorFieldKey;
        if (map.has(operatorKey)) {
          category.children.push({
            title: operatorFieldValue,
            key: operatorKey,
            children: map.get(operatorKey)?.map((item) => {
              return {
                ...item,
                title: (
                  <div
                    className={styles['operator-name']}
                    data-type={item.id}
                    onMouseDown={(e) => startDrag(e, item as any)}
                  >
                    <IconSvg type="workflow-operator" size={16} fill="none" />
                    <Ellipsis tooltip={item.operatorAlias}>{item.operatorAlias}</Ellipsis>

                    <Popover
                      content={
                        <div style={{width: 300}}>
                          <DescriptionList infoList={dealOperatorDetail(item)} colon={false} />
                        </div>
                      }
                      trigger="hover"
                    >
                      <IconSvg
                        className={styles['operator-view']}
                        type="workflow-view"
                        size={16}
                        fill="none"
                      />
                    </Popover>
                  </div>
                ),
                key: item.id
              };
            })
          });
        }
      });
      if (category.children.length > 0) {
        treeArr.push(category);
      }
    });
    setTreeData(treeArr);
  };

  useEffect(() => {
    initOperatorDetail();
  }, []);

  return (
    <div className={styles['dnd-content']} ref={ref}>
      <Tree treeData={treeData} defaultExpandAll={true}></Tree>
    </div>
  );
};

export default React.memo(OperatorDrag);
