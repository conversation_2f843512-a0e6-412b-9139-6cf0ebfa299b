import {Graph} from '@antv/x6';
import React from 'react';

import {Dnd} from '@antv/x6-plugin-dnd';

import {LeftDragTypeEnum} from '@pages/JobWorkflow/constants';
import {IAppState} from '@store/index';
import {useSelector} from 'react-redux';
import OperatorDrag from './OperatorDrag';
import TaskDrag from './TaskDrag';
import styles from './index.module.less';
//定义类型
interface IX6DragProps {
  graph?: React.RefObject<Graph>;
  dnd?: React.RefObject<Dnd>;
  ref?: React.RefObject<HTMLDivElement>;
}
// json 转为 x6 图形
const X6DragPage: React.FC<IX6DragProps> = (props) => {
  const leftDragType = useSelector((state: IAppState) => state.workflowSlice.leftDragType);
  return (
    <div className={styles['dnd-page-container']}>
      {leftDragType === LeftDragTypeEnum.OPERATOR ? <OperatorDrag {...props} /> : <TaskDrag {...props} />}
    </div>
  );
};

export default React.memo(X6DragPage);
