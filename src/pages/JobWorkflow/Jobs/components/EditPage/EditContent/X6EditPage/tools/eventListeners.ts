import {
  JobNodeType<PERSON>num,
  RightDrawerTypeEnum,
  SelectJobNodeTypeEnum,
  X6ShapeTypeEnum
} from '@pages/JobWorkflow/constants';
import {setRightDrawerType, setSelectedNode} from '@store/workflow';
import {getGraph, nodeMap} from '../../../globalVar';
import {copyNode} from './copy';
import {autoParentSize, dealZIndex} from './layout';

/**
 * 初始化事件监听
 * @param graph 图
 */
export const initEventListeners = ({
  dispatch,
  setScale,
  isResultPage,
  isReadOnly
}: {
  dispatch: any;
  setScale: any;
  isResultPage?: boolean;
  isReadOnly: boolean;
}) => {
  const graph = getGraph();

  // 缩放监听 修改缩放比例
  graph.on('scale', ({sx}) => {
    setScale(sx * 100);
  });

  // 只读模式下 不监听事件
  if (isReadOnly) {
    return;
  }

  graph.on('blank:click', () => {
    dispatch(setRightDrawerType(RightDrawerTypeEnum.JOB_CONFIG));
    dispatch(setSelectedNode({id: null, type: SelectJobNodeTypeEnum.NULL}));
  });

  // 节点点击事件 包括操作按钮
  graph.on('node:click', ({e, x, y, node, view}) => {
    const data = node.getData();
    console.log(e, x, y, node, view);

    const btnNode = e.target.closest('[data-btn-type]');
    const btnType = btnNode?.getAttribute('data-btn-type');
    // 点击操作按钮
    if (btnType) {
      if (btnType === SelectJobNodeTypeEnum.DELETE) {
        dispatch(setSelectedNode({id: null, type: SelectJobNodeTypeEnum.NULL}));
        dispatch(setRightDrawerType(RightDrawerTypeEnum.JOB_CONFIG));
        graph?.removeCells([node.id]);
        nodeMap.delete(node.id);
      }
      if (btnType === SelectJobNodeTypeEnum.COPY) {
        copyNode(node, graph!);
      }
      return;
    }

    if (node.shape === X6ShapeTypeEnum.TASK) {
      if (data.type === JobNodeTypeEnum.DATAFLOW_TASK) {
        dispatch(
          setSelectedNode({
            id: node.id,
            type: SelectJobNodeTypeEnum.EDIT_OPERATOR,
            subTaskCode: data.subTaskCode
          })
        );
      } else {
        dispatch(
          setSelectedNode({
            id: node.id,
            type: SelectJobNodeTypeEnum.SAVE_OPERATOR,
            subTaskCode: data.subTaskCode
          })
        );
      }

      dispatch(setRightDrawerType(RightDrawerTypeEnum.TASK_CONFIG));
    } else if (node.shape === X6ShapeTypeEnum.OPERATOR_GROUP) {
      dispatch(
        setSelectedNode({id: data.parent, type: SelectJobNodeTypeEnum.CLICK, subTaskCode: data.subTaskCode})
      );
      dispatch(setRightDrawerType(RightDrawerTypeEnum.TASK_CONFIG));
    } else if (node.shape === X6ShapeTypeEnum.OPERATOR) {
      // 如果 是详情结果页面，则点击算子节点，依旧展示任务节点
      if (isResultPage) {
        dispatch(
          setSelectedNode({
            id: node.getData().parentId,
            type: SelectJobNodeTypeEnum.CLICK,
            subTaskCode: data.subTaskCode
          })
        );
        dispatch(setRightDrawerType(RightDrawerTypeEnum.TASK_CONFIG));
      } else {
        dispatch(setSelectedNode({id: node.id, type: SelectJobNodeTypeEnum.CLICK}));
        dispatch(setRightDrawerType(RightDrawerTypeEnum.OPERATOR_CONFIG));
      }
    }
  });

  // 节点移入 置顶 调整 index 顺序 包括父子节点和连线
  graph.on('node:mouseenter', ({cell}) => {
    dealZIndex(cell, graph);
  });

  // 结果页面下 只监听点击事件 层级关系
  if (isResultPage) {
    return;
  }

  // 节点移除
  graph.on('node:removed', ({node}) => {
    nodeMap.delete(node.id);
    graph?.resize();
  });

  // 节点添加
  graph.on('node:added', ({node}) => {
    const data = node.getData();
    node.toFront();
    nodeMap.set(node.id, data.params);
    if (data.type === JobNodeTypeEnum.OPERATOR_NODE) {
      const parent = graph.getCellById(data.parentId) as any;
      // 延迟一个宏任务 后 自动计算父节点大小
      setTimeout(() => {
        autoParentSize(parent);
        graph?.resize();
      }, 0);
    }
    graph?.resize();
  });
  // 节点位置变化 自动调整父节点大小
  graph.on('node:moving', ({node}) => {
    // 非算子节点 不自动调整大小
    if (node.shape !== X6ShapeTypeEnum.OPERATOR) {
      return;
    }
    // 获取父节点
    const parent = node.getParent();
    autoParentSize(parent as any);
  });

  // 处理连线
  graph.on('edge:mouseenter', ({edge}) => {
    // magnetConnectable
    if (graph.options.interacting) {
      edge.addTools({
        name: 'button-remove',
        args: {
          distance: 0.5 // 距离线中点的位置，0.5 表示正中间
        }
      });
    }
  });

  graph.on('edge:mouseleave', ({edge}) => {
    if (graph.options.interacting) {
      edge.removeTools();
    }
  });

  // 处理节点移入
  graph.on('node:mouseenter', ({node}) => {
    if (graph.options.interacting) {
      node.getPorts().forEach((port) => {
        node.portProp(port.id, 'attrs/circle/style/visibility', 'visible');
      });
    }
  });
  graph.on('node:mouseleave', ({node}) => {
    if (graph.options.interacting) {
      node.getPorts().forEach((port) => {
        node.portProp(port.id, 'attrs/circle/style/visibility', 'hidden');
      });
    }
  });

  // 节点移动后，自动调整大小
  graph.on('node:moved', () => {
    graph.resize();
  });
};
