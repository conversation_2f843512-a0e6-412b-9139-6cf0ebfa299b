/**
 * 任务节点组件
 *
 * 小地图展示组件效果
 * 注册的 task 和 operator 组件
 * 缩放组件
 */
import {Node} from '@antv/x6';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import IconSvg from '@components/IconSvg';
import {SelectJobNodeTypeEnum} from '@pages/JobWorkflow/constants';
import {IAppState} from '@store/index';
import {useMemoizedFn} from 'ahooks';
import React, {useState} from 'react';
import {useSelector} from 'react-redux';
/**
 * 算子节点组件
 * @param node 节点对象
 */
export const OperatorNodeComponent = ({node}: {node: Node}): React.ReactNode => {
  // 算子父节点 id 若有值 在算子编辑界面
  const operateParentId = useSelector((state: IAppState) => state.workflowSlice.operateParentId);
  // 当前选中的节点ID
  const selectedNodeId = useSelector((state: IAppState) => state.workflowSlice.selectedNodeId);
  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  const {name, parentId, taskStatus} = node.getData();
  // 是否悬停
  const [hovered, setHovered] = useState(false);

  // 处理 鼠标悬停
  const handleMouseEnter = useMemoizedFn(() => {
    // 不是编辑当前节点  或者 非编辑状态  不展示
    if ((operateParentId && operateParentId !== parentId) || !isEditing) {
      return;
    }
    setHovered(true);
  });
  const handleMouseLeave = useMemoizedFn(() => {
    setHovered(false);
  });
  return (
    <div
      data-btn-type=""
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className={`x6-component-operator-node ${selectedNodeId === node.id ? 'selected' : ''}  `}
    >
      {/* 工具栏 */}
      {isEditing && (
        <div
          className={`x6-component-task-node-tool`}
          style={{opacity: hovered || selectedNodeId === node.id ? '1' : '0'}}
        >
          <IconSvg className="btn-tool" data-btn-type={SelectJobNodeTypeEnum.COPY} type="copy" />
          <IconSvg className="btn-tool" data-btn-type={SelectJobNodeTypeEnum.DELETE} type="delete" />
        </div>
      )}

      <div className="operator-node-name">
        <div className="operator-node-name-text">
          <Ellipsis tooltip={name}>{name} </Ellipsis>
        </div>
        {/* {taskStatus && (
          <div className="operator-node-status">
            <span
              className="circle"
              style={{backgroundColor: JobInstanceTaskStatusMap[taskStatus].color}}
            ></span>
            {JobInstanceTaskStatusMap[taskStatus].label}
          </div>
        )} */}
      </div>
    </div>
  );
};
