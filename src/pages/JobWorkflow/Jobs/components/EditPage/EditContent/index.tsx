import {Col, Row} from 'acud';
import React from 'react';
import {useSelector} from 'react-redux';
import {IAppState} from '@store/index';
import JsonEditPage from './JsonEditPage';
import X6EditPage from './X6EditPage';

const EditContent: React.FC = () => {
  const isJsonPage = useSelector((state: IAppState) => state.workflowSlice.isJsonPage);

  return (
    <>
      <div className="flex-1 h-full overflow-hidden">{isJsonPage ? <JsonEditPage /> : <X6EditPage />}</div>
    </>
  );
};

export default EditContent;
