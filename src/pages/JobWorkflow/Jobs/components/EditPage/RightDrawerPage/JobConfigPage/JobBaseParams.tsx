import {IJob} from '@api/job';
import {Clipboard} from '@baidu/bce-react-toolkit';
import EditableContent from '@components/EditableContent';
import IconSvg from '@components/IconSvg';
import {IAppState} from '@store/index';
import {setJobName} from '@store/workflow';
import {RULE} from '@utils/regs';
import {Button, Form, Input} from 'acud';
import React, {useEffect, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {jobData} from '../../globalVar';
import styles from '../index.module.less';

const JobBaseParams: React.FC = () => {
  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  const jobName = useSelector((state: IAppState) => state.workflowSlice.jobName);
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [jobDetail, setJobDetail] = useState<IJob | null>(null);
  // 初始化表单
  useEffect(() => {
    setJobDetail(jobData.value);
    form.setFieldsValue(jobData.value);
    form.validateFields({dirty: false});
  }, []);

  useEffect(() => {
    form.setFieldsValue({name: jobName});
    setJobDetail(jobData.value);
    jobData.value.name = jobName;
  }, [jobName]);

  // 修改表单 更新全局变量
  const changeForm = (_: any, values: IJob) => {
    jobData.value = {...jobData.value, ...values};
  };

  // 修改工作流名称
  const changeName = (value: string) => {
    dispatch(setJobName(value));
  };

  return (
    <>
      <Form
        inputMaxWidth={'100%'}
        className={styles['form-container']}
        labelAlign="left"
        layout="horizontal"
        name="basic"
        form={form}
        labelWidth={70}
        colon={false}
        onValuesChange={changeForm}
      >
        <Form.Item
          label="工作流名称"
          name="name"
          rules={[
            {required: isEditing ? true : false, message: '请输入工作流名称'},
            {pattern: RULE.workflowName, message: RULE.workflowNameText}
          ]}
        >
          <EditableContent isEditing={isEditing} onChange={(e) => changeName(e.target.value)}>
            <Input forbidIfLimit={true} limitLength={256} placeholder="请输入描述" allowClear />
          </EditableContent>
        </Form.Item>
        <Form.Item label="工作流 ID">
          {jobDetail?.jobId}
          <Clipboard text={jobDetail?.jobId} className={'inline-block'}>
            <Button icon={<IconSvg type="copy" size={14} />} type="actiontext" />
          </Clipboard>
        </Form.Item>
        <Form.Item label="创建时间">{jobDetail?.createdAt}</Form.Item>
        <Form.Item label="修改时间">{jobDetail?.updatedAt || '-'}</Form.Item>
        <Form.Item label="创建人">{jobDetail?.createUser || '-'}</Form.Item>
        <Form.Item label="最后修改人">{jobDetail?.updateUser || '-'}</Form.Item>
        <Form.Item label="描述" name="description">
          <EditableContent isEditing={isEditing}>
            <Input.TextArea forbidIfLimit={true} limitLength={500} placeholder="请输入描述" allowClear />
          </EditableContent>
        </Form.Item>
      </Form>
    </>
  );
};

export default JobBaseParams;
