import {IKeyValue} from '@api/job';
import JobGlobalParamsFormItem from '@pages/JobWorkflow/Jobs/components/FormItem/JobGlobalParamsFormItem';
import {IAppState} from '@store/index';
import {Form, Table} from 'acud';
import React, {useEffect} from 'react';
import {useSelector} from 'react-redux';
import {jobData} from '../../globalVar';
import styles from '../index.module.less';

const JobGlobalParams: React.FC = () => {
  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  const [form] = Form.useForm();
  // 初始化表单
  useEffect(() => {
    const globalParams = jobData.value.globalParams || [{}];
    form.setFieldsValue({globalParams});
    // 等待更新后再校验 TODO 后期需要优化 目前 0 的话 还是会有时候没有校验
    setTimeout(() => {
      form.validateFields();
    }, 100);
  }, []);

  // 修改表单 更新全局变量
  const changeForm = (_: any, values: {globalParams: IKeyValue[]}) => {
    jobData.value.globalParams = values.globalParams;
  };

  const columns = [
    {
      title: '参数名',
      dataIndex: 'key',
      key: 'key'
    },
    {
      title: '参数值',
      dataIndex: 'value',
      key: 'value'
    }
  ];

  return (
    <>
      {isEditing ? (
        <Form
          inputMaxWidth={'100%'}
          className={styles['form-container']}
          labelAlign="left"
          layout="horizontal"
          name="global"
          form={form}
          labelWidth={70}
          onValuesChange={changeForm}
        >
          <JobGlobalParamsFormItem keyWidth="1 0 150px" disabled={!isEditing} />
        </Form>
      ) : (
        <Table dataSource={jobData.value?.globalParams} columns={columns} pagination={false} />
      )}
    </>
  );
};

export default JobGlobalParams;
