import FlexDrawerArr from '@components/FlexDrawerArr';
import IconSvg from '@components/IconSvg';
import {IAppState} from '@store/index';
import React, {useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import X6EditPage from '../../EditContent/X6EditPage';
import JobBaseParams from './JobBaseParams';
import JobCronParams from './JobCronParams';
import JobGlobalParams from './JobGlobalParams';

const JobConfigPage: React.FC = () => {
  // 是否编辑
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const isJsonPage = useSelector((state: IAppState) => state.workflowSlice.isJsonPage);
  const rightDrawer = useSelector((state: IAppState) => state.workflowSlice.rightDrawer);
  const json = useSelector((state: IAppState) => state.workflowSlice.jsonData);

  useEffect(() => {
    setActiveIndex(0);
  }, [isJsonPage]);
  useEffect(() => {
    setActiveIndex(rightDrawer.index);
  }, [rightDrawer]);
  return (
    <>
      {/* 右侧 作业基本信息 + 可视化预览 */}
      <FlexDrawerArr
        activeIndex={activeIndex}
        changeIndex={setActiveIndex}
        changeVisible={(flag) => {
          if (!flag) {
            setActiveIndex(undefined);
          }
        }}
        iconTitleArr={[
          {icon: <IconSvg type="workflow-drawer-base-info" />, title: '基本信息'},
          {icon: <IconSvg type="workflow-drawer-global-params" />, title: '全局参数'},
          {icon: <IconSvg type="workflow-drawer-schedule-strategy" />, title: '调度策略'},
          isJsonPage && {icon: <IconSvg type="workflow-x6" />, title: '可视化预览'}
        ]}
      >
        <JobBaseParams />
        <JobGlobalParams />
        <JobCronParams />
        {isJsonPage && <X6EditPage jsonStr={json} isReadOnly={true} />}
      </FlexDrawerArr>
    </>
  );
};

export default JobConfigPage;
