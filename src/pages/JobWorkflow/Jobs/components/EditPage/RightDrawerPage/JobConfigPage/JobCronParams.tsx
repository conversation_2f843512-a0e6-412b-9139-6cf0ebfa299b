import {ICronForm} from '@api/job';
import EditableContent from '@components/EditableContent';
import IconSvg from '@components/IconSvg';
import {JobScheduleStatusChinese, JobScheduleStatusEnum} from '@pages/JobWorkflow/constants';
import JobCronFormItem from '@pages/JobWorkflow/Jobs/components/FormItem/JobCronFormItem';
import {dealCronToForm, dealFormToCron} from '@pages/JobWorkflow/tools';
import {IAppState} from '@store/index';
import {DatePicker, Form} from 'acud';
import moment from 'moment';
import React, {useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import JobCronShowTimeItem from '../../../FormItem/JobCronShowTimeItem';
import {jobData} from '../../globalVar';
const JobCronParams: React.FC = () => {
  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  const [scheduleStatus, setScheduleStatus] = useState<JobScheduleStatusEnum>(JobScheduleStatusEnum.OFF);
  const [form] = Form.useForm();

  // 初始化表单
  useEffect(() => {
    setScheduleStatus(jobData.value.scheduleStatus);

    const scheduleConf = jobData.value.scheduleConf;
    const cronForm = dealCronToForm(scheduleConf);

    form.setFieldsValue(cronForm);
    setTimeout(() => {
      form.validateFields();
    }, 0);
  }, []);

  // 修改表单 更新全局变量
  const changeForm = (_: any, values: ICronForm) => {
    const scheduleConf = dealFormToCron(values);
    jobData.value.scheduleConf = scheduleConf;
    console.log('scheduleConf', scheduleConf);
  };

  return (
    <>
      <div className={'form-title'}>
        <IconSvg size={16} type="workflow-detail" />
        <span className={'form-title-text'}>策略信息</span>
      </div>

      <Form
        inputMaxWidth={'100%'}
        labelAlign="left"
        layout="horizontal"
        name="global"
        form={form}
        className="mb-2"
        labelWidth={70}
        onValuesChange={changeForm}
        colon={false}
      >
        <Form.Item label="调度状态">{JobScheduleStatusChinese[scheduleStatus]}</Form.Item>
        <Form.Item label="起始时间" name="startTime">
          <EditableContent isEditing={isEditing} dealValue={(value) => value?.format('YYYY-MM-DD HH:mm:ss')}>
            <DatePicker
              className="w-full"
              disabled={!isEditing}
              showTime
              showNow={false}
              clearIcon={false}
              disabledDate={(current) => current > moment(form.getFieldValue('endTime'))}
            />
          </EditableContent>
        </Form.Item>

        <Form.Item label="终止时间" name="endTime">
          <EditableContent isEditing={isEditing} dealValue={(value) => value?.format('YYYY-MM-DD HH:mm:ss')}>
            <DatePicker
              className="w-full"
              disabled={!isEditing}
              showTime
              showNow={false}
              clearIcon={false}
              disabledDate={(current) => current < moment(form.getFieldValue('startTime'))}
            />
          </EditableContent>
        </Form.Item>

        <JobCronFormItem disabled={!isEditing} form={form} onChange={changeForm} />
      </Form>

      <JobCronShowTimeItem form={form} />
    </>
  );
};

export default JobCronParams;
