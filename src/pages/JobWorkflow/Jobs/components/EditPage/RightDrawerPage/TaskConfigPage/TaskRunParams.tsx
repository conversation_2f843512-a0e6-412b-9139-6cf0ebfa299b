import {ComputeResourceItem, getComputeResourceList} from '@api/Compute';
import EditableContent from '@components/EditableContent';
import {STATUS} from '@pages/Compute/config';
import {WorkspaceContext} from '@pages/index';
import {JobNodeTypeEnum} from '@pages/JobWorkflow/constants';
import {IAppState} from '@store/index';
import urls from '@utils/urls';
import {Form, InputNumber, Link, Select, Tag} from 'acud';
import {useMemoizedFn} from 'ahooks';
import React, {useContext, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router-dom';
import {IJsonNodeData} from '../../EditContent/X6EditPage/type';
import {nodeMap} from '../../globalVar';

import styles from './taskRunParams.module.less';

// 任务运行参数
const TaskRunParams: React.FC = () => {
  const navigate = useNavigate();
  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  // 选中的节点id
  const selectedNodeId = useSelector((state: IAppState) => state.workflowSlice.selectedNodeId);
  // 工作空间id
  const {workspaceId} = useContext(WorkspaceContext);
  const [form] = Form.useForm();
  const [detail, setDetail] = useState<IJsonNodeData | null>(null);
  // 计算集群列表
  const [clusterList, setClusterList] = useState<ComputeResourceItem[]>([]);

  // 修改表单 更新全局变量
  const changeForm = async (_: any, values: any) => {
    const oldObj = nodeMap.get(selectedNodeId) as IJsonNodeData;
    const newObj = {
      ...oldObj,
      taskParam: {
        ...oldObj.taskParam,
        ...values
      }
    };
    nodeMap.set(selectedNodeId, newObj);
  };

  // 搜索计算集群
  const getClusterList = useMemoizedFn(async (flag: boolean) => {
    if (flag) {
      console.log('searchClusterList');
      const res = await getComputeResourceList({
        workspaceId: workspaceId,
        pageNo: 1,
        pageSize: 10000
      });
      // TODO Ray 等待合并代码 需要修改，目前先写死
      setClusterList(res.result?.computes.filter((item) => item.engine === 'Ray'));
    }
  });

  // 初始化表单
  useEffect(() => {
    getClusterList(clusterList?.length === 0);
    const obj = nodeMap.get(selectedNodeId) as IJsonNodeData;

    setDetail(obj);
    form.setFieldsValue(obj.taskParam);
    // 等待更新后再校验 TODO 后期需要优化 目前 0 的话 还是会有时候没有校验
    setTimeout(() => {
      form.validateFields();
    }, 100);
  }, [selectedNodeId]);

  const statusMap = {
    [String(STATUS.DEPLOY)]: 'status-active',
    [String(STATUS.RUNNING)]: 'status-success',
    [String(STATUS.INVALID)]: 'status-error',
    [String(STATUS.CREATED_FAIL)]: 'status-error'
  };

  return (
    <>
      <Form
        inputMaxWidth={'100%'}
        labelAlign="left"
        layout="horizontal"
        name="basic"
        form={form}
        labelWidth={70}
        colon={false}
        onValuesChange={changeForm}
      >
        {detail?.type === JobNodeTypeEnum.DATAFLOW_TASK && (
          <Form.Item label="并发数" name="parallel">
            <EditableContent isEditing={isEditing}>
              <InputNumber min={1} max={10} symmetryMode placeholder="请输入" />
            </EditableContent>
          </Form.Item>
        )}
        <Form.List name="clusterList">
          {(fields) => (
            <>
              {fields.map(({key, name, ...restField}) => (
                <>
                  <Form.Item label="计算资源类型" key={key} {...restField} name={[name, 'clusterType']}>
                    {/* <EditableContent isEditing={isEditing}>
                      <Input readOnly placeholder="请输入参数名" />
                    </EditableContent> */}
                    常驻
                    {/* 目前只有常驻 */}
                  </Form.Item>
                  <Form.Item label="计算资源引擎" key={key} {...restField} name={[name, 'engineType']}>
                    <EditableContent isEditing={isEditing}>
                      {/* 目前只有RAY */}
                      <Select className="w-full" value={'RAY'}>
                        <Select.Option value="RAY">RAY</Select.Option>
                      </Select>
                    </EditableContent>
                  </Form.Item>
                  <Form.Item
                    label="计算集群"
                    key={key}
                    {...restField}
                    name={[name, 'clusterId']}
                    className={styles['cluster-form-item']}
                    rules={[{required: isEditing ? true : false, message: '请选择计算集群'}]}
                  >
                    <EditableContent
                      isEditing={isEditing}
                      dealValue={(value) => clusterList.find((item) => item.computeId === value)?.name}
                    >
                      <Select
                        className="w-full "
                        placeholder="请选择计算集群"
                        showSearch
                        allowClear
                        onDropdownVisibleChange={getClusterList}
                        dropdownMatchSelectWidth={false}
                        dropdownRender={(menu) => (
                          <>
                            {menu}
                            <div className={styles['cluster-footer']}>
                              前往
                              <Link
                                onClick={() =>
                                  window.open(
                                    `${window.location.pathname}#${urls.compute}?workspaceId=${workspaceId}`,
                                    '_blank'
                                  )
                                }
                              >
                                计算资源管理
                              </Link>
                            </div>
                          </>
                        )}
                      >
                        {clusterList?.map((item) => (
                          <Select.Option
                            key={item.computeId}
                            value={item.computeId}
                            disabled={item.status !== STATUS.RUNNING}
                          >
                            <div className={styles['cluster-item']}>
                              <Tag
                                color="transparent"
                                icon={<span className={`circle ${statusMap[item.status]}`}></span>}
                              >
                                {item.name}
                              </Tag>
                              {/* 暂时写死 */}
                              <Tag>AI 增强版 1.0</Tag>
                            </div>
                          </Select.Option>
                        ))}
                      </Select>
                    </EditableContent>
                  </Form.Item>
                </>
              ))}
            </>
          )}
        </Form.List>
      </Form>
    </>
  );
};

export default TaskRunParams;
