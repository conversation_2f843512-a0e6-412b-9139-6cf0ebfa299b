import {Clipboard, Ellipsis} from '@baidu/bce-react-toolkit';
import EditableContent from '@components/EditableContent';
import IconSvg from '@components/IconSvg';
import {JobNodeTypeEnum} from '@pages/JobWorkflow/constants';
import {IAppState} from '@store/index';
import {setEditNodeData} from '@store/workflow';
import {Button, Form, Input, Table} from 'acud';
import {cloneDeep} from 'lodash';
import React, {useEffect, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import JobGlobalParamsFormItem from '../../../FormItem/JobGlobalParamsFormItem';
import {IJsonNodeData} from '../../EditContent/X6EditPage/type';
import {nodeMap} from '../../globalVar';

const TaskBaseParams: React.FC = () => {
  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  // 选中的 id
  const selectedNodeId = useSelector((state: IAppState) => state.workflowSlice.selectedNodeId);
  const [form] = Form.useForm();
  const [formTaskParam] = Form.useForm();
  const dispatch = useDispatch();
  const [detail, setDetail] = useState<IJsonNodeData | null>(null);
  // 初始化表单
  useEffect(() => {
    if (!selectedNodeId) {
      return;
    }
    const obj = nodeMap.get(selectedNodeId) as IJsonNodeData;
    const objClone = cloneDeep(obj);
    setDetail(objClone);
    // 设置表单
    form.setFieldsValue(objClone);
    if (objClone?.taskParam) {
      formTaskParam.setFieldsValue({
        ...objClone.taskParam,
        envVars: objClone.taskParam?.envVars || [{key: '', value: ''}]
      });
    }

    // 等待更新后再校验 TODO 后期需要优化 目前 0 的话 还是会有时候没有校验
    setTimeout(() => {
      form.validateFields();
      formTaskParam.validateFields();
    }, 100);
  }, [selectedNodeId]);

  // 修改表单 更新全局变量
  const changeForm = async (_: any, values: any) => {
    const oldObj = nodeMap.get(selectedNodeId);
    const newObj = {
      ...oldObj,
      name: values.name,
      description: values.description
    };
    nodeMap.set(selectedNodeId, newObj);
    dispatch(setEditNodeData({id: selectedNodeId, name: values.name}));
  };

  // 修改任务参数表单
  const changeTaskParamForm = async (_: any, values: any) => {
    const valuesClone = cloneDeep(values);

    if (valuesClone?.envVars) {
      valuesClone.envVars = valuesClone.envVars?.filter((item) => item.key || item.value);
    }
    const oldObj = nodeMap.get(selectedNodeId) as IJsonNodeData;

    const newObj = {
      ...oldObj,
      taskParam: {
        ...oldObj.taskParam,
        ...valuesClone
      }
    };
    nodeMap.set(selectedNodeId, newObj);
  };

  const columns = [
    {
      title: '参数名',
      dataIndex: 'key',
      key: 'key',
      width: 70,
      ellipsis: true,
      render: (text: string) => {
        return <Ellipsis tooltip={text}>{text}</Ellipsis>;
      }
    },
    {
      title: '参数值',
      dataIndex: 'value',
      key: 'value',
      width: 100,
      ellipsis: true,
      render: (text: string) => {
        return <Ellipsis tooltip={text}>{text}</Ellipsis>;
      }
    }
  ];

  return (
    <>
      <Form
        inputMaxWidth={'100%'}
        labelAlign="left"
        layout="horizontal"
        name="basic"
        form={form}
        labelWidth={70}
        onValuesChange={changeForm}
        colon={false}
      >
        <Form.Item
          label="任务节点名称"
          name="name"
          rules={[{required: isEditing ? true : false, message: '请输入任务节点名称'}]}
        >
          <EditableContent isEditing={isEditing}>
            <Input forbidIfLimit={true} limitLength={256} placeholder="请输入" allowClear />
          </EditableContent>
        </Form.Item>
        <Form.Item label="任务节点 ID">
          {selectedNodeId}
          <Clipboard text={selectedNodeId} className={'inline-block'}>
            <Button icon={<IconSvg type="copy" size={14} />} type="actiontext" />
          </Clipboard>
        </Form.Item>
        <Form.Item label="任务组件">{detail?.type}</Form.Item>
        <Form.Item label="描述" name="description">
          <EditableContent isEditing={isEditing}>
            <Input.TextArea forbidIfLimit={true} limitLength={500} placeholder="请输入描述" allowClear />
          </EditableContent>
        </Form.Item>
      </Form>
      <Form
        inputMaxWidth={'100%'}
        labelAlign="left"
        layout="horizontal"
        name="taskParam"
        form={formTaskParam}
        labelWidth={70}
        colon={false}
        onValuesChange={changeTaskParamForm}
      >
        {detail?.type === JobNodeTypeEnum.RAY_TASK && (
          <>
            <Form.Item
              label="运行代码路径"
              name="codePath"
              rules={[{required: isEditing ? true : false, message: '请输入运行代码路径'}]}
            >
              <EditableContent isEditing={isEditing}>
                <Input forbidIfLimit={true} limitLength={256} placeholder="请输入" allowClear />
              </EditableContent>
            </Form.Item>
            <Form.Item
              label="入口命令"
              name="entryPoint"
              rules={[{required: isEditing ? true : false, message: '请输入入口命令'}]}
            >
              <EditableContent isEditing={isEditing}>
                <Input forbidIfLimit={true} limitLength={256} placeholder="请输入" allowClear />
              </EditableContent>
            </Form.Item>
            <Form.Item label="环境变量" style={{marginBottom: 0}}></Form.Item>
            {isEditing ? (
              <JobGlobalParamsFormItem keyWidth="1 0 150px" formName="envVars" />
            ) : (
              <Table dataSource={detail.taskParam.envVars} columns={columns} />
            )}
          </>
        )}
      </Form>
    </>
  );
};

export default TaskBaseParams;
