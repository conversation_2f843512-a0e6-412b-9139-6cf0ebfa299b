import FlexDrawerArr from '@components/FlexDrawerArr';
import IconSvg from '@components/IconSvg';
import {IAppState} from '@store/index';
import React, {useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import TaskBaseParams from './TaskBaseParams';
import TaskRunParams from './TaskRunParams';

const TaskConfigPage: React.FC = () => {
  // 是否编辑
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const selectedNodeId = useSelector((state: IAppState) => state.workflowSlice.selectedNodeId);
  const rightDrawer = useSelector((state: IAppState) => state.workflowSlice.rightDrawer);

  useEffect(() => {
    if (selectedNodeId) {
      setActiveIndex(0);
    }
  }, [selectedNodeId]);

  useEffect(() => {
    setActiveIndex(rightDrawer.index);
  }, [rightDrawer]);

  return (
    <>
      {/* 右侧 作业基本信息 + 可视化预览 */}
      <FlexDrawerArr
        activeIndex={activeIndex}
        changeIndex={setActiveIndex}
        changeVisible={(flag) => {
          if (!flag) {
            setActiveIndex(undefined);
          }
        }}
        iconTitleArr={[
          {icon: <IconSvg type="workflow-drawer-base-info" />, title: '基本信息'},
          {icon: <IconSvg type="workflow-drawer-run-params" />, title: '执行资源'}
        ]}
      >
        <TaskBaseParams />
        <TaskRunParams />
      </FlexDrawerArr>
    </>
  );
};

export default TaskConfigPage;
