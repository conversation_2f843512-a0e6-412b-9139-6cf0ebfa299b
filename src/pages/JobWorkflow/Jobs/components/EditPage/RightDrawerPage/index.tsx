/**
 * 右侧抽屉 主要分为 工作流配置、任务配置、算子配置、
 * <AUTHOR>
 */
import {RightDrawerTypeEnum} from '@pages/JobWorkflow/constants';
import {IAppState} from '@store/index';
import React from 'react';
import {useSelector} from 'react-redux';
import JobConfigPage from './JobConfigPage';
import OperatorConfigPage from './OperatorConfigPage';
import TaskConfigPage from './TaskConfigPage';

const RightDrawerPage: React.FC = () => {
  const rightDrawer = useSelector((state: IAppState) => state.workflowSlice.rightDrawer);

  // 渲染右侧抽屉
  const renderRightDrawer = () => {
    switch (rightDrawer.type) {
      case RightDrawerTypeEnum.JOB_CONFIG:
        return <JobConfigPage />;
      case RightDrawerTypeEnum.TASK_CONFIG:
        return <TaskConfigPage />;
      case RightDrawerTypeEnum.OPERATOR_CONFIG:
        return <OperatorConfigPage />;
      default:
        return null;
    }
  };

  return <>{renderRightDrawer()}</>;
};

export default RightDrawerPage;
