// 编辑界面样式
// .form-container {
//   :global {
//     .acud-form-item {
//       margin-bottom: 8px;
//     }
//     .acud-form-item-label {
//       padding-left: 0px;
//     }
//     .acud-form-item-label-left label {
//       color: #84868c;
//     }
//     .acud-form-item-control-input {
//       color: #151b26;
//       word-break: break-word; /* 兼容性较好 */
//       word-wrap: break-word; /* 兼容旧版 */
//     }
//     .acud-time-input-wrap {
//       width: 100%;
//     }
//   }
// }
// .form-title {
//   font-size: 14px;
//   color: #151b26;
//   font-weight: 500;
//   line-height: 22px;
//   .form-title-text {
//     padding-left: 8px;
//   }
// }
