.header-btn {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 40px;
  padding: 8px 12px;
  border-bottom: 1px solid #d4d6d9;
  padding-left: 8px;
  :global {
    .acud-btn {
      padding: 4px;
      min-width: 0;
      font-weight: normal;
      &:hover {
        background: rgba(7, 12, 20, 0.06);
        color: #151b26;
      }
    }
  }

  .left-btn {
    flex: 1;
  }
  .right-btn {
    flex: 0 0 500px;
    align-items: right;
    display: flex;
    justify-content: flex-end;

    .param-btn {
      flex: 0 0 52px;
      margin: 0 12px;
    }
    .show-type-btn {
      flex: 0 0 146px;
    }
  }

  .alert-box {
    position: fixed;
  }
}
