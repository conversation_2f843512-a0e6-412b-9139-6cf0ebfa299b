import {JobNodeTypeEnum} from '@pages/JobWorkflow/constants';
import React, {useEffect, useState} from 'react';
import {IJsonNodeData} from '../EditContent/X6EditPage/type';
import {jobData, nodeMap} from '../globalVar';
import styles from './headerParams.module.less';

const HeaderParams: React.FC = () => {
  const [rayTask, setRayTask] = useState<string[]>([]);
  const dealParams = (params: {key: string; value: string}[]) => {
    if (!params) {
      return '-';
    }
    return params
      .filter((item) => item?.key)
      .map((item) => item.key + ' = ' + item.value)
      .join(' , ');
  };

  useEffect(() => {
    const rayTask = Array.from(nodeMap.keys()).filter((key) => {
      const node = nodeMap.get(key);
      return 'type' in node && node?.type === JobNodeTypeEnum.RAY_TASK;
    });
    setRayTask(rayTask);
  }, []);
  return (
    <>
      <div className={styles['header-params']}>
        <div className={styles['params-container']}>
          <div className={styles['title']}>全局参数</div>
          <div className={styles['params-item']}>{dealParams(jobData?.value?.globalParams)}</div>
        </div>
        <div className={styles['params-container']}>
          <div className={styles['title']}>任务参数</div>
          {rayTask?.length === 0 ? '-' : ''}
          {rayTask?.map((key) => {
            const node = nodeMap.get(key) as IJsonNodeData;

            return (
              <div key={key} className={`${styles['params-item']} ${styles['task-item']}`}>
                <div>{node?.name}</div>
                {node?.taskParam?.envVars?.length > 0 ? dealParams(node?.taskParam?.envVars) : '-'}
              </div>
            );
          })}
        </div>
      </div>
    </>
  );
};

export default HeaderParams;
