/**
 * 作业 全局参数 表单项
 * 提供列表页面 和 可视化 编辑界面 全局参数 ray 任务 表单项
 * 可以配置 表单名称
 * 默认最多 50 条
 *
 */
import IconSvg from '@components/IconSvg';
import {RULE} from '@utils/regs';
import {Button, Col, Form, Input, Row} from 'acud';
import {OutlinedPlus} from 'acud-icon';
import React from 'react';
import styles from './index.module.less';

// 参数名 最大长度
interface IGlobalParams {
  disabled?: boolean;
  formName?: string;
  maxLength?: number;
  keyMaxLength?: number;
  valueMaxLength?: number;
  keyWidth?: string;
  keyRule?: any[];
}
// key value  参数配置
const JobGlobalParamsFormItem: React.FC<IGlobalParams> = ({
  disabled,
  formName = 'globalParams',
  maxLength = 50,
  keyMaxLength = 64,
  keyRule = [{pattern: RULE.workflowGlobalParamsKey, message: RULE.workflowGlobalParamsKeyText}],
  valueMaxLength = 128,
  keyWidth = '1 0 184px'
}) => {
  return (
    <>
      <Form.List name={formName}>
        {(fields, {add, remove}) => (
          <>
            {fields.map(({key, name, ...restField}) => (
              <Row key={key} gutter={8} className={styles['global-params-form-item']}>
                <Col flex={keyWidth}>
                  <Form.Item
                    {...restField}
                    name={[name, 'key']}
                    initialValue={''}
                    dependencies={[
                      [formName, name, 'value'],
                      ...fields.map(({name}) => [formName, name, 'key'])
                    ]}
                    rules={[
                      ...keyRule,
                      ({getFieldValue, getFieldsValue}) => ({
                        validator(_, value) {
                          // 只有 value 不为空 才必填
                          const valueField = getFieldValue([formName, name, 'value']);
                          if (valueField && !value) {
                            return Promise.reject(new Error('请输入参数名'));
                          }

                          // 检查重复性
                          if (value) {
                            const allFields = getFieldsValue()[formName] || [];
                            const duplicateCount = allFields.filter(
                              (field: any, index: number) => field?.key === value && index !== name
                            ).length;

                            if (duplicateCount > 0) {
                              return Promise.reject(new Error('参数名不能重复'));
                            }
                          }

                          return Promise.resolve();
                        }
                      })
                    ]}
                  >
                    <Input
                      className="w-full"
                      disabled={disabled}
                      allowClear
                      forbidIfLimit={true}
                      limitLength={keyMaxLength}
                      placeholder="请输入参数名"
                    />
                  </Form.Item>
                </Col>
                <Col flex={'1 1 280px'}>
                  <Form.Item {...restField} name={[name, 'value']} initialValue={''}>
                    <Input
                      className="w-full"
                      disabled={disabled}
                      forbidIfLimit={true}
                      allowClear
                      limitLength={valueMaxLength}
                      placeholder="参数值"
                    />
                  </Form.Item>
                </Col>
                <Col flex={'0 0 16px'}>
                  {
                    <Button
                      className={styles['btn-delete']}
                      type="text"
                      disabled={fields?.length <= 1}
                      onClick={() => remove(name)}
                    >
                      <IconSvg size={16} type="delete" />
                    </Button>
                  }
                </Col>
              </Row>
            ))}
            <Form.Item>
              <Button
                className={styles['btn-add']}
                disabled={disabled || fields.length >= maxLength}
                onClick={() => add({key: '', value: ''})}
                block
                icon={<OutlinedPlus />}
                type="actiontext"
              >
                添加参数
              </Button>
            </Form.Item>
          </>
        )}
      </Form.List>
    </>
  );
};

export default JobGlobalParamsFormItem;
