/**
 * 作业 定时任务 表单项
 * 提供列表页面 和 可视化 编辑界面 cron 表单项
 * 提供类型 配置
 */
import IconSvg from '@components/IconSvg';
import {dealFormToCron} from '@pages/JobWorkflow/tools';
import {Form, Tag, Timeline} from 'acud';
import {FormInstance} from 'acud/lib/form';
import CronExpressionParser from 'cron-parser';
import moment from 'moment';
import React, {useEffect, useState} from 'react';
import styles from './jobCronShowTimeItem.module.less';

const JobCronShowTimeItem: React.FC<{form: FormInstance}> = ({form}) => {
  // 执行时间列表
  const [runTimeList, setRunTimeList] = useState<string[]>([]);

  // 获取执行时间
  const getRunTime = (cronForm: {startTime?: string | moment.Moment; crontab?: string}) => {
    const options = {
      currentDate: moment.max(moment(cronForm?.startTime), moment()).format('YYYY-MM-DD HH:mm:ss')
      // strict: true
    };
    let interval;

    if (!cronForm?.crontab || cronForm?.crontab.split(' ').some((item) => !item)) {
      setRunTimeList([]);
      return;
    }
    try {
      interval = CronExpressionParser.parse(cronForm?.crontab, options);
    } catch (err) {
      console.log('Error:', err);
      setRunTimeList([]);
      return;
    }

    const list = [];

    const endTime = moment(form.getFieldValue('endTime'));
    for (let i = 0; i < 5; i++) {
      const nextTime = moment(interval.next().toString());
      if (nextTime.isAfter(endTime)) {
        break;
      }
      list.push(nextTime.format('YYYY-MM-DD HH:mm:ss'));
    }
    setRunTimeList(list);
  };
  const count = Form.useWatch([], {form});

  useEffect(() => {
    const scheduleConf = dealFormToCron(count);
    getRunTime(scheduleConf);
  }, [count]);

  return (
    <>
      <div className={styles['cron-show-time-item-title']}>
        <IconSvg size={16} type="workflow-cron" fill="none" />
        <span className={'font-bold'}>执行时间预览</span>
        <Tag color="#e8e9eb">仅展示5条</Tag>
      </div>
      <div className={styles['cron-show-time-item-timeline']}>
        <Timeline mode={'vertical'}>
          {runTimeList.map((item) => (
            <Timeline.Item key={item} label={item} color={'#2468F2'} />
          ))}
        </Timeline>
      </div>
    </>
  );
};

export default JobCronShowTimeItem;
