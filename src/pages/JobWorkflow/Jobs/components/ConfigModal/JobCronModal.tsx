/**
 * 新建 cron 弹窗
 */
import {getSchedule, updateSchedule} from '@api/job';
import {WorkspaceContext} from '@pages/index';
import {dealCronToForm, dealFormToCron} from '@pages/JobWorkflow/tools';
import {RULE} from '@utils/regs';
import {DatePicker, Form, Input, Modal, toast} from 'acud';
import {useMemoizedFn} from 'ahooks';
import React, {useContext, useEffect, useState} from 'react';
import {IJobModalProps} from '.';
import JobCronFormItem from '../FormItem/JobCronFormItem';
import styles from './index.module.less';
import JobCronShowTimeItem from '../FormItem/JobCronShowTimeItem';
import moment from 'moment';

const JobCronModal: React.FC<IJobModalProps> = (props: IJobModalProps) => {
  const {jobId, jobName, onSubmit} = props;
  const [isVisible, setIsVisible] = useState(false);
  const {workspaceId} = useContext(WorkspaceContext);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 提交表单
  const handleOk = useMemoizedFn(async () => {
    setLoading(true);

    const values = form.getFieldsValue();

    const {success} = await updateSchedule(workspaceId, jobId, dealFormToCron(values));

    if (success) {
      toast.success({message: '更新成功', duration: 5});
      setIsVisible(false);
      onSubmit(true);
    }

    setLoading(false);
  });
  // 关闭弹窗
  const handleCancel = useMemoizedFn(() => {
    onSubmit(false);
    setIsVisible(false);
  });

  const initFn = useMemoizedFn(async () => {
    const {result} = await getSchedule(workspaceId, jobId);

    const values = dealCronToForm(result);
    form.setFieldsValue({
      name: jobName,
      ...values
    });
    setLoading(false);
  });

  useEffect(() => {
    initFn();
    setIsVisible(true);
  }, []);

  return (
    <Modal
      closable={true}
      width={500}
      title={'调度策略'}
      visible={isVisible}
      onOk={handleOk}
      confirmLoading={loading}
      onCancel={handleCancel}
      className={styles['job-create-modal']}
    >
      <Form name="basic" layout="vertical" labelAlign="left" form={form} inputMaxWidth="500px">
        <Form.Item label="工作流名称" name="name">
          <Input
            disabled
            placeholder={RULE.workflowNameText}
            allowClear
            forbidIfLimit={true}
            limitLength={256}
          />
        </Form.Item>

        <Form.Item label="起始时间" name="startTime">
          <DatePicker
            className="w-full"
            showTime
            showNow={false}
            clearIcon={false}
            disabledDate={(current) => current > moment(form.getFieldValue('endTime'))}
          />
        </Form.Item>

        <Form.Item label="终止时间" name="endTime">
          <DatePicker
            className="w-full"
            showTime
            showNow={false}
            clearIcon={false}
            disabledDate={(current) => current < moment(form.getFieldValue('startTime'))}
          />
        </Form.Item>

        <JobCronFormItem form={form} />

        <JobCronShowTimeItem form={form} />
      </Form>
    </Modal>
  );
};

export default JobCronModal;
