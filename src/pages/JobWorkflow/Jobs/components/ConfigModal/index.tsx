import {JobModalTypeEnum} from '@pages/JobWorkflow/constants';
import React from 'react';
import JobCreateModal from './JobCreateModal';
import JobCronModal from './JobCronModal';
import JobRunInstanceModal from './JobRunInstanceModal';

/** 创建报表弹窗属性 */
export interface IJobModalProps {
  // 弹窗类型
  modalType?: JobModalTypeEnum;
  // 任务 id
  jobId?: string;
  // 任务名称
  jobName?: string;
  // 确定按钮回调函数 flag 控制是否刷新页面
  onSubmit?: (flag: boolean) => void;
}

const ModalManager: React.FC<IJobModalProps> = (props: IJobModalProps) => {
  switch (props.modalType) {
    case JobModalTypeEnum.CRON:
      return <JobCronModal {...props} />;
    case JobModalTypeEnum.RUN_INSTANCE:
      return <JobRunInstanceModal {...props} />;
    case JobModalTypeEnum.JSON:
    case JobModalTypeEnum.EMPTY:
      return <JobCreateModal {...props} />;
    default:
      return null;
  }
};

export default ModalManager;
