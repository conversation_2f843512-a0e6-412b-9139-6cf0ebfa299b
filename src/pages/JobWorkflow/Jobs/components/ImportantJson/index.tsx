import {IJob} from '@api/job';
import {ITemplate, queryTemplateList} from '@api/template';
import {WorkspaceContext} from '@pages/index';
import {JSON_FORMAT} from '@pages/JobWorkflow/constants';
import {Alert, Button, Form, Modal, Select, Space, toast, Upload} from 'acud';
import {OutlinedButtonUpload} from 'acud-icon';
import {useMemoizedFn} from 'ahooks';
import React, {useContext, useEffect, useState} from 'react';
import styles from './index.module.less';

/** 创建工作流导入弹窗属性 */
export interface ImportantJsonModalProps {
  onChange: (job: IJob) => void;
  children?: React.ReactNode;
}

const ImportantJsonModal: React.FC<ImportantJsonModalProps> = ({onChange, children}) => {
  const [isVisible, setIsVisible] = useState(false);
  const {workspaceId} = useContext(WorkspaceContext);
  const [form] = Form.useForm();
  const [job, setJob] = useState<IJob>();
  // 查询字段
  const [templateArr, setTemplateArr] = useState<ITemplate[]>([]);
  // 文件上传字段 控制文件上传后显示文件名和禁用按钮
  const [fileJson, setFileJson] = useState<{
    name?: string;
    json?: string;
    disabled: boolean;
  } | null>(null);
  // 查询模板列表
  const queryList = useMemoizedFn(async () => {
    setFileJson(null);
    const {result} = await queryTemplateList(workspaceId);
    setTemplateArr(result.result);
  });

  useEffect(() => {
    // 打开弹窗 初始化表单
    if (isVisible) {
      form.resetFields();
      queryList();
    }
  }, [isVisible]);

  // 上传文件前检查
  const beforeUpload = useMemoizedFn((file: File) => {
    const reader = new FileReader();
    reader.addEventListener(
      'load',
      () => {
        const jobJsonStr = reader.result!.toString();
        let job: IJob;
        let code;
        try {
          job = JSON.parse(jobJsonStr); // 尝试解析 JSON

          code = JSON.parse(job?.code);
        } catch {
          toast.error({
            message: '文件格式错误，请检查json文件',
            duration: 5
          });
          return;
        }

        // 格式化 json
        const codeStr = JSON.stringify(code, null, JSON_FORMAT);
        // 然后这将显示一个文本文件
        setFileJson({
          name: file.name,
          json: codeStr,
          disabled: false
        });
        setJob(job);

        form.setFieldValue('code', jobJsonStr);
        form.validateFields(['code']);
      },
      false
    );
    reader.readAsText(file);
    return Upload.LIST_IGNORE;
  });
  // 切换模板
  const changeTemplate = useMemoizedFn((value: string) => {
    setJob({});
    setFileJson({disabled: !!value, json: value});
  });
  const handleOk = useMemoizedFn(() => {
    let json = String(fileJson?.json);
    try {
      json = JSON.stringify(JSON.parse(json), null, JSON_FORMAT);
    } catch (error) {
      toast.error({
        message: '文件格式错误，请检查json文件',
        duration: 5
      });
    }
    onChange({...job, code: json});
    setIsVisible(false);
  });
  const handleCancel = useMemoizedFn(() => {
    setIsVisible(false);
  });
  return (
    <>
      <span onClick={() => setIsVisible(true)}>{children}</span>
      <Modal
        closable={true}
        width={500}
        title="导⼊⼯作流"
        visible={isVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        okButtonProps={{disabled: !fileJson?.json}}
        className={styles['job-create-modal']}
      >
        <Alert
          className={styles['alert']}
          message="导⼊新⼯作流后，原有的⼯作流将会被清空，请谨慎操作"
          banner
        />

        <Form name="basic" layout="vertical" labelAlign="left" form={form} inputMaxWidth="500px">
          <Space align="start">
            <Form.Item
              label="选择模板："
              name="code"
              rules={[
                {
                  required: true,
                  message: '请选择模板或上传文件'
                }
              ]}
              extra="可以从模板中导入，也可以自定义上传，支持json格式"
            >
              <Select
                className={styles['template-select']}
                allowClear
                placeholder="请选择模板"
                options={[...templateArr, {templateName: fileJson?.name, jobTemplate: fileJson?.json}]
                  .filter((item) => item.templateName!)
                  .map((item) => {
                    return {
                      label: String(item.templateName),
                      value: item.jobTemplate!
                    };
                  })}
                onChange={changeTemplate}
              ></Select>
            </Form.Item>
            <Upload accept="application/json" desPosition="right" beforeUpload={beforeUpload}>
              <Button
                className={styles['upload-btn']}
                disabled={fileJson?.disabled}
                icon={<OutlinedButtonUpload />}
              >
                上传文件
              </Button>
            </Upload>
          </Space>
        </Form>
      </Modal>
    </>
  );
};

export default ImportantJsonModal;
