import {Plus1} from '@baidu/xicon-react-bigdata';
import {
  <PERSON>ton,
  Dropdown,
  Link,
  Menu,
  Modal,
  Pagination,
  Popover,
  Search,
  Space,
  Switch,
  Table,
  toast,
  Tooltip
} from 'acud';
import {useRequest} from 'ahooks';
import React, {useCallback, useContext, useEffect, useRef, useState} from 'react';
import {useNavigate} from 'react-router-dom';

import {copyJob, deleteJob, IJob, queryJobList, switchSchedule} from '@api/job';
import RefreshButton from '@components/RefreshButton';

import useUrlState from '@ahooksjs/use-url-state';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import IconSvg from '@components/IconSvg';
import {WorkspaceContext} from '@pages/index';
import {OperateType} from '@utils/enums';
import urls from '@utils/urls';
import {downloadStr} from '@utils/utils';
import {
  JobDetailPageTypeEnum,
  JobModalTypeEnum,
  JobScheduleStatusChinese,
  JobScheduleStatusEnum,
  WorkflowPageCreateTypeEnum
} from '../constants';
import {dealCronToStr, dealExportJob} from '../tools';
import ModalManager, {IJobModalProps} from './components/ConfigModal';
import JobEmptyPage from './components/EmptyPage';
import styles from './index.module.less';

const Jobs: React.FC = () => {
  const navigate = useNavigate();
  // 是否展示空页面 查询为空并且 总数为 0
  const [showCreatePage, setShowCreatePage] = useState(false);
  // 数据源
  const [dataSource, setDataSource] = useState<{
    result: {
      jobs: IJob[];
      total: number;
    };
  }>({result: {jobs: [], total: 0}});
  // 定时器
  const timerRef = useRef(null);
  const paramsRef = useRef({});

  const [, setUrlParams] = useUrlState();

  const {workspaceId} = useContext(WorkspaceContext);
  // 表格排序
  const [sorter, setSorter] = useState({});
  const [pagination, setPagination] = useState<{
    pageNo: number;
    pageSize?: number;
  }>({
    pageNo: 1
  });
  // 查询字段
  const [keyword, setKeyword] = useState('');

  // 刷新时间
  const refreshTime = 3000;
  // 默认支持名称查询
  const [keywordType] = useState('name');
  const [jobModal, setJobModal] = useState<IJobModalProps>({});
  const [filters, setFilters] = useState<{
    scheduleStatus?: JobScheduleStatusEnum[];
  }>({});

  // 刷新页面(单独使用 避免 loading 状态 错误)
  const {run: reFreshFn} = useRequest(queryJobList, {
    manual: true,
    onSuccess: (res) => {
      setDataSource(res);
      if (res?.result?.jobs?.some((job) => job.scheduleStatus === JobScheduleStatusEnum.PENDING)) {
        timerRef.current = setTimeout(() => {
          reFreshFn(paramsRef.current, workspaceId);
        }, refreshTime);
      }
    }
  });

  const {loading, run: runQueryJobList} = useRequest(
    () => queryJobList({...pagination, sorter, keyword, keywordType, filters}, workspaceId),
    {
      refreshDeps: [pagination, sorter, filters], //  分页 和 排序变化时刷新
      onBefore: () => {
        clearTimeout(timerRef.current);
      },
      onSuccess: (res) => {
        setDataSource(res);
        // 如果查询结果为空，并且没有关键字，并且没有筛选条件，则展示空页面
        setShowCreatePage(res.result.total === 0 && !keyword && !filters);
        if (res?.result?.jobs?.some((job) => job.scheduleStatus === JobScheduleStatusEnum.PENDING)) {
          //  保存查询条件
          paramsRef.current = {
            ...pagination,
            sorter,
            keyword,
            keywordType,
            filters
          };
          timerRef.current = setTimeout(() => {
            reFreshFn(paramsRef.current, workspaceId);
          }, refreshTime);
        }
      }
    }
  );

  // 查询的时候 恢复到第一页
  const searchJob = () => {
    setPagination((obj) => ({...obj, pageNo: 1}));
  };
  // 工作流操作方法
  const jobFn = async (job: IJob, type: OperateType) => {
    switch (type) {
      case OperateType.RUN: {
        // 运行
        setJobModal({modalType: JobModalTypeEnum.RUN_INSTANCE, jobId: job.jobId, jobName: job.name});
        break;
      }
      case OperateType.COPY: {
        await copyJob(job, WorkflowPageCreateTypeEnum.COPY);
        await searchJob();
        break;
      }
      case OperateType.EXPORT: {
        downloadStr(dealExportJob(job), `${job.name}.json`);
        break;
      }
      case OperateType.EDIT: {
        // 导航到编辑页面
        navigate(`${urls.jobDetail}?jobId=${job.jobId}&edit=true`);
        break;
      }
      case OperateType.DETAIL: {
        // 导航到编辑页面
        navigate(`${urls.jobDetail}?jobId=${job.jobId}`);
        break;
      }
      case OperateType.LOG: {
        // 导航到实例列表
        navigate(`${urls.jobDetail}?jobId=${job.jobId}&type=${JobDetailPageTypeEnum.JOB_INSTANCES}`);
        break;
      }
      case OperateType.SWITCH_SCHEDULE: {
        // 切换调度状态
        const res = await switchSchedule(
          job.workspaceId!,
          job.jobId!,
          job.scheduleStatus === JobScheduleStatusEnum.ON
            ? JobScheduleStatusEnum.OFF
            : JobScheduleStatusEnum.ON
        );
        // 若切换成功，则刷新页面
        if (res.success) {
          toast.success({message: '调度状态开始切换', duration: 5});
          // 1秒后刷新
          setTimeout(() => {
            searchJob();
          }, 1000);
        }
        break;
      }
      case OperateType.SCHEDULE: {
        // 调度
        setJobModal({modalType: JobModalTypeEnum.CRON, jobId: job.jobId, jobName: job.name});
        break;
      }
      case OperateType.DELETE: {
        Modal.confirm({
          title: '删除确定',
          content: `"${job.name}"删除后，工作流中的运行数据将被清空，无法恢复，请谨慎操作`,
          onOk() {
            // 删除工作流 按钮 loading
            return deleteJob(job.workspaceId!, job.jobId!).then((res) => {
              if (res.success) {
                toast.success({message: '删除成功', duration: 5});
                searchJob();
              } else {
                return Promise.reject();
              }
            });
          }
        });
        break;
      }

      default:
        break;
    }
  };

  const columns: any = [
    {
      title: '工作流名称',
      dataIndex: 'name',
      key: 'name',
      width: 180,
      fixed: 'left',
      ellipsis: {
        showTitle: false
      },
      render: (name, record) => (
        <Tooltip placement="topLeft" title={name}>
          <a onClick={() => jobFn(record, OperateType.DETAIL)}>{name}</a>
        </Tooltip>
      )
    },

    {
      title: '调度状态',
      dataIndex: 'scheduleStatus',
      key: 'scheduleStatus',
      filterMultiple: false,
      filters: [
        {
          text: JobScheduleStatusChinese[JobScheduleStatusEnum.ON],
          value: JobScheduleStatusEnum.ON
        },
        {
          text: JobScheduleStatusChinese[JobScheduleStatusEnum.OFF],
          value: JobScheduleStatusEnum.OFF
        },
        {
          text: JobScheduleStatusChinese[JobScheduleStatusEnum.PENDING],
          value: JobScheduleStatusEnum.PENDING
        }
      ],
      width: 180,
      render: (scheduleStatus, record) => {
        return (
          <>
            {/* 必须有调度 才可以开启调度 */}
            {record?.scheduleConf?.type ? (
              <Switch
                checked={scheduleStatus === JobScheduleStatusEnum.ON}
                loading={scheduleStatus === JobScheduleStatusEnum.PENDING}
                disabled={scheduleStatus === JobScheduleStatusEnum.PENDING}
                onChange={() => {
                  jobFn(record, OperateType.SWITCH_SCHEDULE);
                }}
              />
            ) : (
              <Popover
                content={
                  <div className="container">
                    <p className="content">
                      未设置调度策略，无法开启调度
                      <Button
                        type="actiontext"
                        onClick={() => {
                          jobFn(record, OperateType.SCHEDULE);
                        }}
                      >
                        立即设置
                      </Button>
                    </p>
                  </div>
                }
                trigger="hover"
              >
                <Switch disabled={true} checked={scheduleStatus === JobScheduleStatusEnum.ON} />
              </Popover>
            )}
            {record.errMsg && (
              <Tooltip title={record.errMsg}>
                <IconSvg size={16} className="pl-2" type="warning" color="#FF9326" />
              </Tooltip>
            )}
          </>
        );
      }
    },
    {
      title: '调度策略',
      dataIndex: 'scheduleConf',
      key: 'scheduleConf',
      width: 180,
      render: (scheduleConf, record) => {
        return (
          <div className={styles['schedule-conf']}>
            <Ellipsis tooltip={dealCronToStr(scheduleConf)}>{dealCronToStr(scheduleConf)}</Ellipsis>
            {scheduleConf?.crontab && (
              <Tooltip title={scheduleConf?.crontab}>
                <Button
                  className={'edit-button'}
                  type="actiontext"
                  icon={<IconSvg type="question" fill="none" color="#84868C" />}
                ></Button>
              </Tooltip>
            )}
            {/* 鼠标移入展示 */}
            <Button
              className={'edit-button'}
              type="actiontext"
              // 调度中或开启调度 不能编辑
              disabled={
                record.scheduleStatus === JobScheduleStatusEnum.ON ||
                record.scheduleStatus === JobScheduleStatusEnum.PENDING
              }
              icon={<IconSvg type="edit" color="#84868C" />}
              onClick={() => {
                jobFn(record, OperateType.SCHEDULE);
              }}
            ></Button>
          </div>
        );
      }
    },
    {
      title: '创建人',
      dataIndex: 'createUser',
      key: 'createUser',
      width: 180
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      sorter: true
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 180,
      sorter: true
    },

    {
      title: '操作',
      key: 'operate',
      width: 180,
      fixed: 'right',
      render: (record: IJob) => {
        return (
          <Space>
            <Link key={OperateType.RUN} onClick={() => jobFn(record, OperateType.RUN)}>
              运行
            </Link>
            <Link key={OperateType.EDIT} onClick={() => jobFn(record, OperateType.EDIT)}>
              编辑
            </Link>
            <Link key={OperateType.LOG} onClick={() => jobFn(record, OperateType.LOG)}>
              运行记录
            </Link>
            <Dropdown
              overlayStyle={{width: 100}}
              label={<IconSvg type="more" size={16} />}
              overlay={
                <Menu>
                  <Menu.Item key={OperateType.COPY} onClick={() => jobFn(record, OperateType.COPY)}>
                    复制
                  </Menu.Item>
                  <Menu.Item key={OperateType.EXPORT} onClick={() => jobFn(record, OperateType.EXPORT)}>
                    导出
                  </Menu.Item>
                  <Menu.Item
                    disabled={record.scheduleStatus === JobScheduleStatusEnum.ON}
                    key={OperateType.DELETE}
                    onClick={() => jobFn(record, OperateType.DELETE)}
                  >
                    删除
                  </Menu.Item>
                </Menu>
              }
            />
          </Space>
        );
      }
    }
  ];

  const showTotal = useCallback(() => {
    return dataSource?.result?.total ? `共 ${dataSource?.result?.total} 条` : '';
  }, [dataSource?.result?.total]);

  // 处理表格变化（分页、排序、筛选）
  const handleTableChange = (pagination, filters, sorter) => {
    setSorter(sorter);
    setFilters(filters);
  };
  /**
   * 处理新建弹窗关闭
   * @param flag 是否刷新页面
   */
  const handleCreateJob = (flag: boolean) => {
    if (flag) {
      searchJob();
    }
    setUrlParams({create: undefined});
    setJobModal({modalType: null});
  };

  // 清除定时器
  useEffect(() => {
    return () => {
      clearTimeout(timerRef.current);
    };
  }, []);

  return (
    <div>
      {showCreatePage ? (
        <JobEmptyPage setJobModal={setJobModal} />
      ) : (
        <>
          <div className={styles['operation-container']}>
            <div className="w-[240px]">
              <Search
                placeholder="请输⼊⼯作流名称进⾏搜索"
                className={styles['search-container']}
                allowClear
                onSearch={searchJob}
                onChange={(e) => setKeyword(e.target.value)}
              />
            </div>
            <Space className={styles['right-container']}>
              <RefreshButton onClick={runQueryJobList}></RefreshButton>

              <Button
                onClick={() => {
                  setJobModal({modalType: JobModalTypeEnum.JSON});
                }}
                icon={<IconSvg type="workflow-import" />}
                key="import"
              >
                导入工作流
              </Button>
              <Button
                type="primary"
                key="create"
                icon={<Plus1 theme="line" size={16} strokeLinejoin="round" />}
                onClick={() => {
                  setJobModal({modalType: JobModalTypeEnum.EMPTY});
                }}
              >
                创建工作流
              </Button>
            </Space>
          </div>

          <Table
            className={styles['table-container']}
            scroll={{x: 1228}}
            dataSource={dataSource?.result?.jobs}
            columns={columns}
            rowKey="jobId"
            loading={{
              loading: loading,
              size: 'small'
            }}
            pagination={false}
            onChange={handleTableChange}
          />

          <div className={styles['pagination-container']}>
            <Pagination
              showSizeChanger
              showQuickJumper
              current={pagination.pageNo}
              showTotal={showTotal}
              total={dataSource?.result?.total || 0}
              onChange={(page, pageSize) => {
                setPagination({
                  pageNo: page,
                  pageSize: pageSize
                });
              }}
            />
          </div>
        </>
      )}

      <ModalManager onSubmit={handleCreateJob} {...jobModal} />
    </div>
  );
};

export default Jobs;
