.operation-container {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 16px;

  .right-container {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    .search-container {
      width: 240px;
    }
  }
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;

  :global {
    .acud-pagination {
      width: fit-content;
    }
  }
}
.table-container {
  :global {
    .acud-table-cell-row-hover {
      .edit-button {
        display: inline-block;
      }
    }
    .edit-button {
      display: none;
      height: 20px;
      padding: 0;
    }
  }

  .schedule-conf {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}
