import useUrlState from '@ahooksjs/use-url-state';
import {deleteJob, detailJob, IJob, startJob, switchSchedule} from '@api/job';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import IconSvg from '@components/IconSvg';
import {WorkspaceContext} from '@pages/index';
import JobInstance from '@pages/JobInstance';
import {JobDetailPageTypeEnum, JobScheduleStatusEnum} from '@pages/JobWorkflow/constants';
import {dealExportJob} from '@pages/JobWorkflow/tools';
import {IAppDispatch, IAppState} from '@store/index';
import {clearWorkflowState, setIsEditing, setJobName, setJson, setRefreshTime} from '@store/workflow';
import {OperateType} from '@utils/enums';
import urls from '@utils/urls';
import {downloadStr} from '@utils/utils';
import {<PERSON><PERSON><PERSON>rumb, Button, Dropdown, Input, Link, Loading, <PERSON>u, Modal, Tabs, toast} from 'acud';
import {useMemoizedFn, useRequest} from 'ahooks';
import React, {useContext, useEffect, useRef, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {useNavigate} from 'react-router-dom';
import EditPage from '../components/EditPage';
import {initJobData, jobData, nodeMap} from '../components/EditPage/globalVar';
import styles from './index.module.less';
export interface JobDetailPageRef {
  saveJobFn: (checkJsonFlag: boolean) => Promise<boolean>;
}
const JobDetailPage: React.FC = () => {
  const navigate = useNavigate();
  const editPageRef = useRef<JobDetailPageRef>(null);
  const [{jobId, edit}] = useUrlState();
  const [jobObj, setJobObj] = useState<IJob>();

  const {workspaceId} = useContext(WorkspaceContext);
  const jobName = useSelector((state: IAppState) => state.workflowSlice.jobName);
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  const dispatch = useDispatch<IAppDispatch>();

  // 区分 列表与模板页面
  const [state, setState] = useUrlState<{type: string}>({
    type: JobDetailPageTypeEnum.DETAIL
  });

  // 切换 修改 url 参数 保证刷新也可以正常切换页面
  const onChange = (activeKey: string) => {
    setState({type: activeKey});
  };

  const saveConfirm = useMemoizedFn(async (type: OperateType): Promise<boolean> => {
    let title = '保存提示';
    let checkJsonFlag = true;
    const content = '需要先保存工作流，才能进行操作！';
    switch (type) {
      case OperateType.RUN:
        title = '确定运行当前工作流作业吗？';
        break;
      case OperateType.DELETE:
        title = '确定删除当前工作流作业吗？';
        checkJsonFlag = false;
        break;
      case OperateType.EXPORT:
        title = '确定导出当前工作流作业吗？';
        break;
      case OperateType.SWITCH_SCHEDULE:
        title = '确定开启调度当前工作流作业吗？';
        break;
    }
    return new Promise((resolve, reject) => {
      Modal.confirm({
        title,
        content,
        onOk: async () => {
          const res = await editPageRef.current?.saveJobFn(checkJsonFlag);
          resolve(res);
        },
        onCancel() {
          resolve(false);
        }
      });
    });
  });

  // 运行工作流
  const startJobFn = useMemoizedFn(async () => {
    const runSuccess = '运行提交成功';
    let res = true;
    if (isEditing) {
      res = await saveConfirm(OperateType.RUN);
    }
    if (res) {
      startJob(workspaceId, jobId).then((res) => {
        if (res.success) {
          if (state.type === JobDetailPageTypeEnum.JOB_INSTANCES) {
            dispatch(setRefreshTime(new Date().getTime()));
            toast.success({
              message: runSuccess,
              duration: 5
            });
          } else {
            toast.success({
              message: runSuccess,
              description: (
                <span>
                  请前往运行记录查看结果，立即前往
                  <Link
                    className="global-notify-ticket-link cursor-pointer"
                    onClick={() => navigate(`${urls.jobResult}?jobInstanceId=${res.result}`)}
                  >
                    运行记录
                  </Link>
                </span>
              ),
              duration: 5,
              key: res.result.id
            });
          }
        }
      });
    }
  });

  // 导出工作流
  const exportJobFn = useMemoizedFn(async () => {
    let res = true;
    if (isEditing) {
      res = await saveConfirm(OperateType.EXPORT);
    }
    if (res) {
      downloadStr(dealExportJob(jobData.value), `${jobName}.json`);
    }
  });

  // 开启调度
  const switchScheduleFn = useMemoizedFn(async () => {
    let res = true;
    if (isEditing) {
      res = await saveConfirm(OperateType.SWITCH_SCHEDULE);
    }
    if (res) {
      switchSchedule(workspaceId, jobId, JobScheduleStatusEnum.ON).then((res) => {
        if (res.success) {
          findJobDetail();
          toast.success({
            message: '开启调度成功',
            duration: 5
          });
        }
      });
    }
  });

  // 操作工作流
  const dealJobFn = useMemoizedFn(async (type: OperateType) => {
    switch (type) {
      case OperateType.RUN:
        startJobFn();
        break;
      case OperateType.DELETE:
        Modal.confirm({
          title: '确定删除当前工作流作业吗？',
          content: `“${jobName}”删除后，工作流中的运行数据将被清空，无法恢复，请慎重`,
          onOk: () => {
            deleteJob(workspaceId, jobId).then((res) => {
              if (res.success) {
                toast.success({
                  message: '删除成功',
                  description: '删除成功',
                  duration: 5
                });
                navigate(urls.job);
              }
            });
          }
        });
        break;
      case OperateType.EXPORT:
        exportJobFn();
        break;
      // 实例界面 刷新
      case OperateType.REFRESH:
        dispatch(setRefreshTime(new Date().getTime()));
        break;

      // 开启调度
      case OperateType.SWITCH_SCHEDULE:
        switchScheduleFn();
        break;
    }
  });

  const initData = (jobDetail?: IJob) => {
    // 暂时不处理 调度信息
    // if (!jobDetail?.scheduleConf) {
    //   jobDetail.scheduleConf = dealFormToCron();
    // }
    setJobObj(jobDetail);
    // 记录任务数据
    jobData.value = jobDetail;
    dispatch(setJson(jobDetail?.code || ''));
    dispatch(setJobName(jobDetail?.name || ''));

    // 没有调度
    const isSchedule =
      jobDetail?.scheduleStatus === JobScheduleStatusEnum.ON ||
      jobDetail?.scheduleStatus === JobScheduleStatusEnum.PENDING;

    // 开启调度 则不开启编辑状态
    if (isSchedule) {
      dispatch(setIsEditing(false));
    }
  };
  // 获取工作流详情
  const {loading, run: findJobDetail} = useRequest(() => detailJob(workspaceId, jobId), {
    onSuccess: (res) => {
      clearJobData();
      setTimeout(() => {
        initData(res.result);
      }, 0);
    }
  });

  const clearJobData = useMemoizedFn(() => {
    nodeMap.clear();
    initJobData();
    // 清空工作流状态
    dispatch(clearWorkflowState());
  });

  useEffect(() => {
    // 自动开启编辑状态
    dispatch(setIsEditing(!!edit));
    return () => {
      // 清空工作流状态
      clearJobData();
    };
  }, []);
  const btnMenu = (
    <Menu>
      <Menu.Item onClick={() => dealJobFn(OperateType.EXPORT)}>导出</Menu.Item>
      <Menu.Item onClick={() => dealJobFn(OperateType.DELETE)}>删除</Menu.Item>
      {jobObj?.scheduleStatus === JobScheduleStatusEnum.OFF && (
        <Menu.Item onClick={() => dealJobFn(OperateType.SWITCH_SCHEDULE)}>开启调度</Menu.Item>
      )}
    </Menu>
  );

  return (
    <div className={styles['job-detail']}>
      <div className={styles['detail-page']}>
        <Loading loading={loading} />
        {/* 顶部面包屑 + 标题 */}
        <div className={styles['header']}>
          <Breadcrumb>
            <Breadcrumb.Item>
              <Link onClick={() => navigate(urls.job)} className={styles['breadcrumb-link']}>
                工作流
              </Link>
            </Breadcrumb.Item>

            <Breadcrumb.Item>工作流详情</Breadcrumb.Item>
          </Breadcrumb>
          <div className={styles['title']}>
            <div className="w-1/2">
              {isEditing ? (
                <Input
                  className={styles['title-input']}
                  value={jobName}
                  onChange={(e) => dispatch(setJobName(e.target.value))}
                />
              ) : (
                <Ellipsis tooltip={jobName}>{jobName}</Ellipsis>
              )}
            </div>
            <div className="flex items-center gap-2 float-right">
              <Dropdown overlay={btnMenu}>
                <Button icon={<IconSvg type="more" />}></Button>
              </Dropdown>
              {state.type === JobDetailPageTypeEnum.JOB_INSTANCES && (
                <Button
                  icon={<IconSvg type="refresh" />}
                  onClick={() => dealJobFn(OperateType.REFRESH)}
                ></Button>
              )}
              <Button type="primary" onClick={() => dealJobFn(OperateType.RUN)}>
                立即运行
              </Button>
            </div>
          </div>
        </div>
        {/* 顶部 tab 切换 详情/运行记录 */}
        <Tabs onChange={onChange} activeKey={state.type}>
          <Tabs.TabPane tab={'工作流详情'} key={JobDetailPageTypeEnum.DETAIL}></Tabs.TabPane>
          <Tabs.TabPane
            disabled={isEditing}
            tab={'运行记录'}
            key={JobDetailPageTypeEnum.JOB_INSTANCES}
          ></Tabs.TabPane>
        </Tabs>

        {/* 详情/运行记录 内容 没放在tab 因为需要靠左右边上，放里面有间距 */}
        <div className={styles['container']}>
          {state.type === JobDetailPageTypeEnum.DETAIL ? (
            // 内容整体布局分为左右两部分
            <EditPage jobObj={jobObj} initJobDetail={findJobDetail} ref={editPageRef} />
          ) : (
            <div className={styles['job-instance-container']}>
              <JobInstance jobId={jobId} jobName={jobName} />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default JobDetailPage;
