.job-detail {
  width: 100%;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
  padding: 0 8px 8px 0;
  .detail-page {
    width: 100%;
    height: 100%;
    background-color: #fff;
    border: 1px solid rgba(212, 214, 217, 0.6);
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    overflow: hidden;
    .header {
      padding: 14px 16px 0 16px;
      .breadcrumb-link {
        font-size: 12px;
      }
      .title {
        font-size: 22px;
        line-height: 32px;
        color: #151b26;
        padding-top: 14px;
        padding-bottom: 8px;
        font-weight: 500;
        height: 32px;
        display: flex;
        justify-content: space-between;

        .title-input {
          height: 40px;
          width: 600px;
          margin-left: -10px;
          input {
            font-size: 22px;
            line-height: 32px;
            color: #151b26;
            padding-left: 8px;
            font-weight: 500;
            height: 32px;
            display: flex;
            justify-content: space-between;
          }
          border-color: #fff;
          &:hover {
            background-color: #f7f7f9;
            input {
              background-color: #f7f7f9;
            }
          }
        }
        :global {
          .acud-input-span-focus {
            border-color: #2468f2;
          }
        }
      }
    }
    // 清除 tab 下面多余的 margin
    :global {
      .acud-tabs-top {
        flex: 0 0 36px;
      }
      .acud-tabs-top > .acud-tabs-nav {
        margin: 0;
        margin-left: 16px;
        // 清除 tab 的下 border
        &::before {
          border: none;
        }
      }
    }

    .container {
      border-top: 1px solid #d4d6d9;
      flex: 1;
      overflow: hidden;
      margin-top: -2px;
      // padding-left: 12px;
      .container-row {
        height: 100%;

        .container-left {
          min-width: 0;
          height: 100%;
          display: flex;
          flex-direction: column;
          .left-btn {
            flex: 0 0 40px;
            width: 100%;
            padding: 8px 12px;
            border-bottom: 1px solid #d4d6d9;
            // border-right: 1px solid #d4d6d9;
            padding-left: 8px;
            margin-bottom: 2px;
            :global {
              .acud-btn {
                padding: 4px;
                min-width: 0;
                margin-right: 12px;
                font-weight: normal;
                &:hover {
                  background: rgba(7, 12, 20, 0.06);
                  color: #151b26;
                }
              }
            }
          }

          .monaco {
            // padding-top: 8px;
            flex: 1;
            overflow: hidden;
          }
        }
      }

      .job-instance-container {
        padding: 16px;
        overflow: auto;
        height: 100%;
        box-sizing: border-box;
      }
    }

    // 编辑界面样式
    .form-container {
      padding: 12px;
      :global {
        .acud-form-item {
          margin-bottom: 4px;
        }
        .acud-form-item-label-left label {
          color: #84868c;
        }
        .acud-form-item-control-input {
          color: #151b26;
          word-break: break-word; /* 兼容性较好 */
          word-wrap: break-word; /* 兼容旧版 */
        }
      }
    }
  }
}
