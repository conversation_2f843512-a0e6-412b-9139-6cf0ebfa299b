import {Button, Form, Input, Modal, Select, Space, Upload} from 'acud';
import {useMemoizedFn} from 'ahooks';
import React, {useEffect, useState} from 'react';
import {IJobInstance, operateJobInstance} from '@api/jobInstance';
import {
  JobInstanceOperateTypeEnum,
  JobInstanceRerunTypeEnum,
  JobInstanceStatusEnum
} from '@pages/JobInstance/constants';

/** 重新运行属性 */
export interface IJobInstanceRerunModalProps {
  isVisible: boolean;
  // 区分新建任务类型 空任务 或者 导入 任务
  jobInstance?: IJobInstance;
  // 确定按钮回调函数 flag 控制是否刷新页面
  onSubmit: (flag: boolean) => void;
}

const JobInstanceRerunModal: React.FC<IJobInstanceRerunModalProps> = ({isVisible, jobInstance, onSubmit}) => {
  /** 重新运行类型 */
  const JobInstanceRerunTypeList = [
    {
      label: '全流程重跑',
      value: JobInstanceRerunTypeEnum.FROM_FIRST
    },
    {
      label: '从失败节点重跑',
      value: JobInstanceRerunTypeEnum.FROM_FAILURE
    }
  ];
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 提交表单
  const handleOk = useMemoizedFn(async () => {
    setLoading(true);
    try {
      const obj = await form.validateFields();

      await operateJobInstance(
        jobInstance?.workspaceId,
        jobInstance?.jobInstanceId,
        JobInstanceOperateTypeEnum.RERUN,
        {
          strategy: obj.strategy
        }
      );
      onSubmit(true);
    } catch (error) {
      console.error(error);
    }
    setLoading(false);
  });
  // 关闭弹窗
  const handleCancel = useMemoizedFn(() => {
    onSubmit(false);
  });

  useEffect(() => {
    // 打开弹窗 初始化表单
    if (isVisible) {
      form.setFieldsValue(jobInstance);
      form.setFieldsValue({
        strategy: JobInstanceRerunTypeEnum.FROM_FIRST
      });
    }
  }, [isVisible]);

  return (
    <Modal
      closable={true}
      title={'重跑'}
      visible={isVisible}
      onOk={handleOk}
      confirmLoading={loading}
      onCancel={handleCancel}
    >
      <Form name="basic" labelAlign="left" labelWidth={80} form={form}>
        <Form.Item label="⼯作流任务名称" name="jobName">
          <Input disabled />
        </Form.Item>

        <Form.Item label="重跑策略" name="strategy">
          <Select
            disabled={jobInstance?.status !== JobInstanceStatusEnum.FAILURE}
            style={{width: '100%'}}
            placeholder="请选择重跑策略"
            options={JobInstanceRerunTypeList}
          ></Select>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default JobInstanceRerunModal;
