import {Loading} from 'acud';
import React from 'react';
import ReactMonacoEditor from 'react-monaco-editor';
import styles from './index.module.less';
interface IMonacoEditorLog {
  value?: string;
  loading?: boolean;
}

/**
 * MonacoEditor编辑器组件
 * @param value 编辑器内容 默认空字符串
 * @returns
 */
const MonacoLogComponent: React.FC<IMonacoEditorLog> = ({
  // 编辑器内容
  value = '',
  // 是否显示loading
  loading = false
}) => {
  const handleEditorDidMount = (editor, monaco) => {
    // 定义高亮规则
    const keywords = [
      {word: /\b(error|ERROR|Error)\b/g, className: 'error-highlight'},
      {word: /\b(warning|WARNING|Warning)\b/g, className: 'warning-highlight'},
      {word: /\b(debug|DEBUG|Debug)\b/g, className: 'debug-highlight'},
      {word: /\b(info|INFO|Info)\b/g, className: 'info-highlight'}
    ];

    // 应用高亮
    const text = editor.getValue();
    const decorations = [];

    keywords.forEach(({word, className}) => {
      let match;
      while ((match = word.exec(text)) !== null) {
        const startPos = editor.getModel().getPositionAt(match.index);
        const endPos = editor.getModel().getPositionAt(match.index + match[0].length);

        decorations.push({
          range: new monaco.Range(startPos.lineNumber, startPos.column, endPos.lineNumber, endPos.column),
          options: {
            inlineClassName: className
          }
        });
      }
    });

    // 应用装饰器
    editor.deltaDecorations([], decorations);
  };

  return (
    <div className={styles['log-container']}>
      {loading ? (
        <div className="h-full w-full flex items-center justify-center">
          <Loading>
            <div className="w-80 h-80">加载中...</div>
          </Loading>
        </div>
      ) : (
        <ReactMonacoEditor
          theme="logTheme"
          height="100%"
          language={'text'}
          value={value}
          editorDidMount={handleEditorDidMount}
          options={{
            readOnly: true,
            scrollBeyondLastLine: false, // 关闭滚动超出最后一行
            automaticLayout: true,
            wordWrap: 'off',
            minimap: {
              enabled: false
            }
          }}
        />
      )}
    </div>
  );
};

export default MonacoLogComponent;
