import useUrlState from '@ahooksjs/use-url-state';
import {detailJobInstance, IJobInstance} from '@api/jobInstance';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import {ExclamationCircle2} from '@baidu/xicon-react-bigdata';
import IconSvg from '@components/IconSvg';
import {WorkspaceContext} from '@pages/index';
import {JobDetailPageTypeEnum, RightDrawerTypeEnum} from '@pages/JobWorkflow/constants';
import {jobData} from '@pages/JobWorkflow/Jobs/components/EditPage/globalVar';
import {setIsEditing, setRightDrawerType} from '@store/workflow';
import urls from '@utils/urls';
import {formatSeconds} from '@utils/utils';
import {Breadcrumb, Col, Link, Loading, Row, Space, Tag, Tooltip} from 'acud';
import {useRequest} from 'ahooks';
import React, {useContext, useEffect, useState} from 'react';
import {useDispatch} from 'react-redux';
import {useLocation, useNavigate} from 'react-router-dom';
import {JobInstanceStatusMap} from '..';
import {JobInstanceStatusEnum} from '../constants';
import LeftDataPage from './components/LeftDataPage';
import RightResultPage from './components/RightResultPage';
import styles from './index.module.less';

const JobInstanceResultPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const {workspaceId} = useContext(WorkspaceContext);
  const [{jobInstanceId}] = useUrlState();
  // 是否是作业结果页 区分面包屑
  const isJobPage = location.pathname === urls.jobResult;
  // 整体的实例结果
  const [jobInstance, setJobInstance] = useState<IJobInstance>();
  const dispatch = useDispatch();
  // 设置编辑状态为 false
  useEffect(() => {
    dispatch(setIsEditing(false));
  }, []);

  // 获取作业结果
  const {loading} = useRequest(() => detailJobInstance(workspaceId, jobInstanceId), {
    onSuccess: (res) => {
      const obj = res.result;
      setJobInstance(obj);
      jobData.value = obj;
      dispatch(setRightDrawerType(RightDrawerTypeEnum.JOB_CONFIG));
    }
  });

  return (
    <div className={styles['result-page']}>
      {loading && <Loading />}
      <div className={styles['result-container']}>
        {/* 顶部页面 */}
        <div className={styles['header']}>
          {isJobPage ? (
            <Breadcrumb>
              <Breadcrumb.Item>
                <Link onClick={() => navigate(urls.job)}>工作流列表</Link>
              </Breadcrumb.Item>
              <Breadcrumb.Item>
                <Link
                  onClick={() =>
                    navigate(
                      `${urls.jobDetail}?jobId=${jobInstance?.jobId}&type=${JobDetailPageTypeEnum.JOB_INSTANCES}`
                    )
                  }
                >
                  运行记录
                </Link>
              </Breadcrumb.Item>
              <Breadcrumb.Item>记录详情</Breadcrumb.Item>
            </Breadcrumb>
          ) : (
            <Breadcrumb>
              <Breadcrumb.Item>
                <Link onClick={() => navigate(urls.jobInstance)}>运行记录</Link>
              </Breadcrumb.Item>
              <Breadcrumb.Item>记录详情</Breadcrumb.Item>
            </Breadcrumb>
          )}
          {/* 标题 */}
          <div className={styles['title']}>
            <div className={styles['title-ellipsis']}>
              <Ellipsis tooltip={jobInstance?.jobName}>{jobInstance?.jobName} </Ellipsis>
            </div>

            {/* 实例状态 */}
            <Tooltip
              title={
                jobInstance?.status === JobInstanceStatusEnum.SUBMIT_FAILURE ? jobInstance?.errorMsg : ''
              }
            >
              <Tag
                className={styles['title-tag']}
                color={JobInstanceStatusMap[jobInstance?.jobStatus]?.color}
              >
                <div className="flex items-center gap-1">
                  {JobInstanceStatusMap[jobInstance?.jobStatus]?.label}
                  {/* 是否显示错误提示 */}
                  {jobInstance?.status === JobInstanceStatusEnum.SUBMIT_FAILURE && (
                    <ExclamationCircle2 theme="line" size={16} strokeLinejoin="round" />
                  )}
                </div>
              </Tag>
            </Tooltip>
          </div>
          {/* 工作流 */}
          <div className={styles['description']}>
            <Space split={<>|</>}>
              <span className={styles['icon-text']}>
                <IconSvg type="job-instance-id" size={14} />
                {jobInstance?.jobInstanceId}
              </span>
              <span className={styles['icon-text']}>
                <IconSvg type="job-instance-time" size={14} />
                {formatSeconds(jobInstance?.durationSec)}
              </span>
              <span className={styles['icon-text']}>
                <IconSvg type="job-instance-user" size={14} />
                {jobInstance?.runUsername}
              </span>
            </Space>
          </div>
        </div>
        {/* 内容页面 */}
        <div className={styles['content']}>
          <Row className={styles['content-row']}>
            <Col flex="1 1 0px" className={styles['content-table']}>
              <LeftDataPage jobInstance={jobInstance} />
            </Col>

            <RightResultPage jobInstance={jobInstance} />
          </Row>
        </div>
      </div>
    </div>
  );
};

export default JobInstanceResultPage;
