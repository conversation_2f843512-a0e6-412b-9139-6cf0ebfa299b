import React, {ComponentType, createContext, FC, Suspense, useEffect, useMemo, useState} from 'react';
import {ServiceParam} from '@baidu/bce-react-toolkit';
import {AppLayout} from '@/components/AppLayout';
import {Navigate, Outlet, useLocation, useNavigate} from 'react-router-dom';
import urls, {WORKSPACE_ROUTER_PREFIX} from '@/utils/urls';
import {getWorkspacePermission} from '@api/auth';
import useUrlState from '@ahooksjs/use-url-state';
import {Loading, Menu} from 'acud';
import WorkspaceHeader from '@components/WorkspaceHeader';
import WorkspaceOutHeader from '@components/WorkspaceOutHeader/private-index';
import {useMemoizedFn} from 'ahooks';
import WorkspaceDetail from '@pages/Workspace/WorkspaceDetail';
import WorkspaceList from '@pages/Workspace/WorkspaceList';
import IframeEdapPageView from '@pages/Iframe';
import Metastore from '@pages/Metastore';
import store from '@store/index';
import {MenuItem} from '@type/common';
import {recursiveMenus} from '@utils/utils';
import useAuth from '@hooks/useAuth';
import {hasAccess} from '@utils/auth';
import flags from '@/flags';

/** 权限定义 */
export enum EAuth {
  /** 元数据页 - 读权限 - 包含read、readWrite */
  GlobalMetastoreRead = 'globalMetastoreRead'
}

const JobWorkflow = React.lazy(() => import(/* webpackChunkName: "JobWorkflow" */ '@pages/JobWorkflow'));
const JobDetailPage = React.lazy(
  () => import(/* webpackChunkName: "JobDetailPage" */ '@pages/JobWorkflow/Jobs/components/DetailPage')
);
const JobInstance = React.lazy(() => import(/* webpackChunkName: "JobInstance" */ '@pages/JobInstance'));
const JobInstanceResultPage = React.lazy(
  () => import(/* webpackChunkName: "JobInstanceResultPage" */ '@pages/JobInstance/ResultPage')
);

const TemplateDetail = React.lazy(
  () => import(/* webpackChunkName: "TemplateDetail" */ '@pages/JobWorkflow/Templates/Detail')
);

import MetaData from './MetaData/index';
import IconSvg from '@components/IconSvg';

const WorkArea = React.lazy(() => import(/* webpackChunkName: "WorkArea" */ '@pages/WorkArea'));

const Compute = React.lazy(() => import(/* webpackChunkName: "Compute" */ '@pages/Compute'));

const ComputeCreate = React.lazy(
  () => import(/* webpackChunkName: "ComputeCreate" */ '@pages/Compute/Create')
);

/** 主菜单定义 */
export const menus: MenuItem[] = [
  {
    menuName: '多模态空间',
    key: urls.manageWorkspace,
    isNavMenu: true,
    Component: WorkspaceList,
    isPageWrapperNotRequired: false,
    isPageLayoutCustomized: true,
    icon: <IconSvg type="nav-workarea" size={16} color="#303540" />
  },
  {
    menuName: '结构化空间',
    key: urls.manageEDAPWorkspace,
    isNavMenu: true,
    Component: IframeEdapPageView,
    isPageWrapperNotRequired: false,
    isPageLayoutCustomized: true,
    icon: <IconSvg type="nav-workarea" size={16} color="#303540" />
  },
  {
    menuName: '工作空间详情',
    key: urls.manageWorkspaceDetail,
    isNavMenu: false,
    Component: WorkspaceDetail,
    isPageWrapperNotRequired: true,
    activeMenuKey: '/manage-workspace',
    isPageLayoutCustomized: true
  },
  {
    menuName: '元数据',
    key: urls.metastore,
    isNavMenu: true,
    Component: Metastore,
    isPageWrapperNotRequired: false,
    isPageLayoutCustomized: true,
    icon: <IconSvg type="nav-metadata" size={16} color="#303540" />,
    auth: EAuth.GlobalMetastoreRead
  }
];

/** 空间菜单定义 */
export const workspaceMenus: MenuItem[] = [
  {
    menuName: '工作区',
    key: urls.workArea,
    isNavMenu: true,
    Component: WorkArea,
    isPageWrapperNotRequired: true,
    isPageLayoutCustomized: true,
    icon: <IconSvg type="nav-workarea" size={16} color="#303540" />
  },
  {
    menuName: '元数据',
    key: urls.metaData,
    isNavMenu: true,
    Component: MetaData,
    isPageWrapperNotRequired: true,
    isHeaderNav: false,
    isPageLayoutCustomized: true,
    icon: <IconSvg type="nav-metadata" size={16} color="#303540" />
  },
  {
    menuName: '计算资源',
    key: urls.compute,
    isNavMenu: true,
    Component: Compute,
    isPageWrapperNotRequired: true,
    isPageLayoutCustomized: true,
    icon: <IconSvg type="nav-instance" size={16} color="#303540" />
  },
  {
    menuName: '计算资源新建',
    key: urls.computeCreate,
    isNavMenu: false,
    Component: ComputeCreate,
    isPageWrapperNotRequired: true,
    isPageLayoutCustomized: true
  },
  {
    menuName: '工作流',
    key: urls.job,
    isNavMenu: true,
    Component: JobWorkflow,
    isPageWrapperNotRequired: true,
    isPageLayoutCustomized: true,
    icon: <IconSvg type="nav-workflow" size={16} color="#303540" />
  },
  {
    menuName: '工作流详情页',
    key: urls.jobDetail,
    isNavMenu: false,
    isPageLayoutCustomized: true,
    activeMenuKey: urls.job,
    Component: JobDetailPage
  },
  {
    menuName: '模板',
    key: urls.templateDetail,
    isNavMenu: false,
    activeMenuKey: urls.job,
    Component: TemplateDetail,
    isPageWrapperNotRequired: true,
    isPageLayoutCustomized: true
  },
  {
    menuName: '运行记录结果',
    key: urls.jobResult,
    isNavMenu: false,
    activeMenuKey: urls.job,
    isPageLayoutCustomized: true,
    Component: JobInstanceResultPage
  },

  {
    menuName: '运行记录结果,需在运行记录结果页面中展示s',
    key: urls.jobInstanceResult,
    isNavMenu: false,
    activeMenuKey: urls.jobInstance,
    isPageLayoutCustomized: true,
    Component: JobInstanceResultPage
  },
  {
    menuName: '运行记录',
    key: urls.jobInstance,
    isNavMenu: true,
    isPageLayoutCustomized: true,
    Component: JobInstance,
    icon: <IconSvg type="nav-operate" size={15} color="#303540" />
  }
];

/** 打平之后的主菜单列表 */
export const flattenedMenuList = recursiveMenus(menus);

/** 打平之后的空间菜单列表 */
export const workspaceFlattenedMenuList = recursiveMenus(workspaceMenus);

export interface IWorkspaceContextType {
  workspaceId: string;
}

export const WorkspaceContext = createContext<IWorkspaceContextType>({
  workspaceId: ''
});

const Main: FC<{
  menus: MenuItem[];
  component?: ComponentType<any>;
}> = ({menus, component: Component}) => {
  const [serviceParams, setServiceParams] = useState<ServiceParam[]>([]);

  const globalMetastoreRead = !!useAuth('global', 'metastore');

  // 使用replace 防止破坏history
  const [{workspaceId: urlWorkspaceId}, setUrlParams] = useUrlState(undefined, {
    navigateMode: 'replace'
  });
  const [curWorkspaceId, setCurWorkspaceId] = useState<string>(urlWorkspaceId);
  const [permLoading, setPermLoading] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  // 当前路径是否包含workspace 判断当前是否在workspace空间
  const pathnameHasWorkspace = location.pathname.includes(WORKSPACE_ROUTER_PREFIX);

  const handleWorkspacePerm = async (workspaceId: string) => {
    setPermLoading(true);
    const res = await getWorkspacePermission(workspaceId);
    if (res.success) {
      store.dispatch({
        type: 'workspaceAuth/updateWorkspaceAuth',
        payload: res.result
      });
    }

    const curMenu = recursiveMenus(menus).find((item) => location.pathname === item.key);

    if (!curMenu) {
      // TODO：处理当前页无权限
    }
    // 如果不是menu页签的首页
    else if (curMenu && !curMenu.isNavMenu && curMenu.activeMenuKey) {
      navigate(curMenu.activeMenuKey, {replace: true});
    }
    setPermLoading(false);
  };

  // 项目权限请求
  useEffect(() => {
    if (curWorkspaceId) {
      handleWorkspacePerm(curWorkspaceId);
    }
  }, [curWorkspaceId]);

  const urlChangeFunc = useMemoizedFn(() => {
    // workspace 隐藏头部
    // 退出 workspace 后，显示头部
    const header = document.getElementById('header');
    if (pathnameHasWorkspace && header) {
      header.style.display = 'none';
    } else if (!pathnameHasWorkspace && header) {
      header.style.display = 'flex';
    }

    if (!curWorkspaceId && pathnameHasWorkspace && !urlWorkspaceId) {
      // TODO: 后面根据实际情况 定义到空间列表页面或者概览页面
      navigate(urls.manageWorkspace);
    }

    // 哈希变化 进入 workspace 更新当前 workspaceId
    if (urlWorkspaceId && pathnameHasWorkspace && urlWorkspaceId !== curWorkspaceId) {
      setCurWorkspaceId(urlWorkspaceId);
    }
    // 哈希变化后，如果还在workSpace 同步 workspaceId到url
    else if (!urlWorkspaceId && curWorkspaceId && pathnameHasWorkspace) {
      setUrlParams({workspaceId: curWorkspaceId});
    }
    // 退出 workspace 后，清除 workspaceId
    else if (!pathnameHasWorkspace && curWorkspaceId) {
      setCurWorkspaceId('');
    }
  });

  // 监听url变化
  useEffect(() => {
    urlChangeFunc();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathnameHasWorkspace, urlWorkspaceId]);

  // workspaceContextValue 防止不必要的 re-render
  const workspaceContextValue = useMemo(() => ({workspaceId: curWorkspaceId}), [curWorkspaceId]);

  const computeReadOnly = useAuth('workspace', 'compute') === 'readOnly';
  // 新增
  const menu = (
    <Menu>
      <Menu.Item key="1" onClick={() => navigate(`${urls.job}?workspaceId=${curWorkspaceId}&create=json`)}>
        <div className="flex items-center">
          <IconSvg className="mr-[8px]" type="nav-workflow" size={16} color="#303540" />
          工作流
        </div>
      </Menu.Item>
      {!computeReadOnly ? (
        <Menu.Item key="2" onClick={() => navigate(`${urls.computeCreate}?workspaceId=${curWorkspaceId}`)}>
          <div className="flex items-center">
            <IconSvg className="mr-[8px]" type="nav-instance" size={16} color="#303540" />
            计算资源
          </div>
        </Menu.Item>
      ) : null}
    </Menu>
  );

  // 若有auth字段，则根据权限过滤菜单
  const filteredMenus = useMemo(
    () => menus.filter((route) => hasAccess({authKey: route.auth, globalMetastoreRead})),
    [menus, globalMetastoreRead]
  );

  // 普通用户无元数据管理权限，隐藏全局菜单
  const hideGlobalMenu = useMemo(() => {
    return !globalMetastoreRead && !pathnameHasWorkspace;
  }, [globalMetastoreRead, pathnameHasWorkspace]);
  return (
    <>
      {flags.DatabuilderPrivateSwitch ? (
        <WorkspaceOutHeader workspaceId={curWorkspaceId} />
      ) : curWorkspaceId ? (
        <WorkspaceHeader workspaceId={curWorkspaceId} />
      ) : null}
      <AppLayout
        menuClassName={hideGlobalMenu ? '!hidden' : ''}
        menus={filteredMenus}
        serviceParams={serviceParams}
        {...(pathnameHasWorkspace
          ? {
              sidebarExtra: menu,
              hasMenuHead: false
            }
          : {})}
      >
        {Component && (
          <Suspense fallback={<div>Loading...</div>}>
            <Component />
          </Suspense>
        )}
        {curWorkspaceId && permLoading && <Loading loading />}
        {curWorkspaceId && !permLoading && (
          <WorkspaceContext.Provider value={workspaceContextValue}>
            <Outlet />
          </WorkspaceContext.Provider>
        )}
      </AppLayout>
    </>
  );
};

export default Main;
