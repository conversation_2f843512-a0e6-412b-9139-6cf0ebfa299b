/**
 * @file useAuth
 * @description 获取全局权限，获取工作空间权限
 * <AUTHOR>
 */
import {useSelector} from 'react-redux';
import {IAppState} from '@store/index';
import {PermissionType} from '@api/auth';
import {IWorkspaceAuthState} from '@store/WorkspaceAuth';
import {IGlobalAuthState} from '@store/GlobalAuth';

/**
 * @description 获取权限
 * @param type 'workspace' | 'global'
 * @param name 根据type动态变化的权限名称
 * @returns PermissionType
 */

// 使用条件类型和泛型实现参数类型的动态调整
export default function useAuth<T extends 'workspace' | 'global'>(
  type: T,
  name: T extends 'workspace' ? keyof IWorkspaceAuthState : keyof IGlobalAuthState
): PermissionType | IWorkspaceAuthState['metastoreAdmin'] {
  const sliceName = type === 'workspace' ? 'workspaceAuthSlice' : 'globalAuthSlice';
  const permission = useSelector((state: IAppState) => state[sliceName][name as string]);
  return permission;
}
