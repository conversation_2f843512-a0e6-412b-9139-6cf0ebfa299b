/*
此 less 文件被 以下两个文件共用
- src/components/WorkspaceHeader/index.tsx
- src/components/WorkspaceOutHeader/private-index.tsx
*/
.workspace-header.db-workspace-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  min-width: 600px;
  height: 50px;
  background: #F7F7F9;
  z-index: 999;

  .header-title {
    display: flex;
    align-items: center;
    margin-right: 32px;

    .header-title-text {
      font-size: 18px;
      margin-left: 6px;
      color: #070C14;
      font-weight: 500;
      line-height: 24px;
      opacity: 0.6;
    }

  }

  :global {
    .acud-menu-header-container {
      .acud-menu-header {
        height: 50px;
        background: #F7F7F9;
        box-shadow: unset;

        .acud-menu-header-title {
          width: 264px;

          &>a {
            font-size: 18px;
            color: #151B26;
            line-height: 32px;
            margin-right: 16px;
          }
        }

        .acud-menu-header-logo {
          height: unset;
          max-height: 50px;
        }

        .acud-menu-header-nav {
          flex-grow: 0;
          min-width: unset;
          .acud-select-selector {
            background: rgba(7,12,20,0.06);
            border-radius: 5px;
            border: 0;
          }
        }
      }
    }
  }

  .right-info {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    height: 50px;

    &-region {
      margin-right: 16px;
    }

    &-dropdown {
      display: flex;

      &-item {
        margin-right: 16px;
      }
    }
  }
}

.workspace-select-dropdown {
  width: 222px;

  &-footer {
    padding: 10px 6px 6px 6px;
    border-top: 1px solid #E6E6E8;
    font-size: 12px;
    color: #84868C;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
