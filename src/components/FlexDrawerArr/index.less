.drawer-drag-container {
  width: 1px;
  background-color: #e1e2e4;
  height: 100%;
  position: absolute;
}

.drawer-draggable {
  cursor: col-resize;
  -webkit-user-drag: none;
  z-index: 999;

  &:hover {
    background-color: #2468f2;
    width: 4px;
  }
}

.drawer-left {
  position: relative;
  flex: 0 0 400px;
  overflow: hidden;
  height: 100%;

  flex-direction: column;

  .drawer-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    .drawer-title {
      height: 40px;
      font-size: 14px;
      font-weight: 600;
      padding: 8px 4px 8px 16px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #e1e2e4;

      &-right-options {
        margin-left: auto;
      }
    }
    .drawer-children {
      flex: 1;
      overflow: auto;
    }

    // 抽屉表单样式
    .acud-form-item {
      margin-bottom: 8px;
    }
    .show-detail {
      .acud-form-item {
        margin-bottom: 2px;
      }
    }
    .acud-form-item-label {
      padding-left: 0px;

      // word-break: break-word;
      // overflow-wrap: break-word;
      // white-space: normal;
    }
    .acud-form-item-label-left label {
      color: #5c5f56;
    }
    .acud-form-item-control-input {
      color: #151b26;
      word-break: break-word; /* 兼容性较好 */
      word-wrap: break-word; /* 兼容旧版 */
    }
    .acud-form-item-control {
      min-width: 0;
    }
    .acud-time-input-wrap {
      width: 100%;
    }

    .form-title {
      font-size: 14px;
      color: #151b26;
      font-weight: 500;
      line-height: 22px;
      .form-title-text {
        padding-left: 8px;
      }
    }
  }
}
.drawer-right {
  flex: 0 0 40px;
  // background-color: #fbfbfc;
  border-left: 1px solid #e1e2e4;
  padding: 8px;
  .drawer-right-btn-active {
    background: rgba(7, 12, 20, 0.06);
    border-radius: 4px;
    color: #151b26;
  }
}
