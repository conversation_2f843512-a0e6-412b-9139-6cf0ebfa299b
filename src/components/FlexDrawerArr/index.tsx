// 右侧抽屉 可托拽 可关闭

import {Close1, Refresh} from '@baidu/xicon-react-bigdata';
import {Button, Space, Tooltip} from 'acud';
import {useEventListener} from 'ahooks';
import React, {useEffect, useRef, useState} from 'react';
import './index.less';
/**
 * 抽屉数组配置
 * @param children 子组件数组
 * @param defaultOpen 默认是否打开
 * @param defaultIndex 默认选中第几个
 * @param title 标题
 * @param closeAble 是否可关闭 默认 true
 * @param draggable 是否可拖拽 默认 true
 * @param iconTitleArr 图标和标题数组 & 可刷新配置 & 强制渲染 forceRender 默认 false
 * @param minWidth 最小宽度 默认 320
 * @param maxWidth 最大宽度 默认 800
 * @param rightMinWidth 右侧最小宽度 默认 1000
 * @param activeIndex 切换的 index
 * @param changeIndex 切换 index 的回调
 * @param changeClose 切换关闭的回调
 */
interface IFlexDrawerArr {
  children?: React.ReactNode[];
  defaultOpen?: boolean;
  defaultIndex?: number;
  closeAble?: boolean;
  draggable?: boolean;
  iconTitleArr?: {
    icon: React.ReactNode;
    title?: string;
    forceRender?: boolean;
    handleRefreshFn?: () => void;
  }[];
  minWidth?: number;
  maxWidth?: number;
  rightMinWidth?: number;
  activeIndex?: number;
  changeIndex?: (index: number) => void;
  changeVisible?: (visible: boolean) => void;
  padding?: string;
}
const FlexDrawerArr: React.FC<IFlexDrawerArr> = ({
  children,
  defaultOpen = true,
  closeAble = true,
  draggable = true,
  defaultIndex = 0,
  iconTitleArr = [],
  minWidth = 320,
  maxWidth = 800,
  rightMinWidth = 1000,
  activeIndex,
  changeIndex,
  changeVisible,
  padding = '12px 16px'
}) => {
  const [visible, setVisible] = useState(defaultOpen);
  const [drawerIndex, setDrawerIndex] = useState<number>(defaultIndex);
  const dragRef = useRef<HTMLDivElement>(null);
  const [dragging, setDragging] = useState(0);
  const [drawerWidth, setDrawerWidth] = useState(320);

  const [drawerMaxWidth, setDrawerMaxWidth] = useState(Math.min(maxWidth, window.innerWidth - rightMinWidth));

  // 如果后面有内容区太多的情况 再加下节流
  const handleMouseMove = (e: MouseEvent) => {
    if (dragging - e.clientX < minWidth || dragging - e.clientX > drawerMaxWidth) {
      return;
    }
    setDrawerWidth(dragging - e.clientX);
  };

  useEventListener(
    ['mousemove', 'mouseup'],
    (e) => (e.type === 'mouseup' ? setDragging(0) : handleMouseMove(e as MouseEvent)),
    {
      enable: !!dragging
    }
  );

  useEventListener(['mousedown'], (e) => setDragging(e.clientX + drawerWidth), {
    target: dragRef
  });

  // 监控页面宽度变化 设置最小宽度
  const monitorPageWidth = () => {
    setDrawerWidth(minWidth);
    setDrawerMaxWidth(Math.min(maxWidth, window.innerWidth - rightMinWidth));
  };

  useEffect(() => {
    window.addEventListener('resize', monitorPageWidth);
    return () => {
      window.removeEventListener('resize', monitorPageWidth);
    };
  }, []);

  useEffect(() => {
    if (activeIndex !== undefined) {
      setDrawerIndex(activeIndex);
    }
  }, [activeIndex]);

  return (
    <>
      <div
        className={'drawer-left'}
        style={{
          display: visible ? 'flex' : 'none',
          flex: `0 0 ${drawerWidth}px`
        }}
      >
        {/* 拖拽线 避免影响其他组件 */}
        <div
          ref={draggable ? dragRef : null}
          className={`drawer-drag-container ${draggable ? 'drawer-draggable' : ''}`}
          style={{display: visible ? 'flex' : 'none'}}
        ></div>
        {/* 都需要渲染 使用 none 隐藏，避免表单无法找到对象 */}
        {children?.map((item, index) => (
          <div
            className={'drawer-container'}
            style={{
              display: index === drawerIndex ? 'flex' : 'none'
            }}
            key={String(iconTitleArr[index]?.title)}
          >
            {/* 标题和关闭按钮 */}
            <div className={'drawer-title'}>
              {iconTitleArr[index]?.title}

              <div className={'drawer-title-right-options'}>
                {iconTitleArr[index]?.handleRefreshFn && (
                  <Button
                    icon={<Refresh size={16} />}
                    onClick={() => iconTitleArr[index]?.handleRefreshFn?.()}
                    type="text"
                  ></Button>
                )}
                {closeAble && (
                  <Button
                    className={'close-btn'}
                    icon={<Close1 size={16} />}
                    onClick={() => {
                      setVisible(false);
                      changeIndex?.(-1);
                      changeVisible?.(false);
                      setDrawerIndex(-1);
                    }}
                    type="text"
                  ></Button>
                )}
              </div>
            </div>
            <div
              className={'drawer-children'}
              style={{
                padding
              }}
            >
              {/* 内容 */}
              {(iconTitleArr[index]?.forceRender || index === drawerIndex) && children?.[index]}
            </div>
          </div>
        ))}
      </div>

      {/* 按钮列表 */}
      <div className={'drawer-right'}>
        <Space direction="vertical">
          {iconTitleArr.map((item, index) => (
            <Tooltip key={index} title={item.title || ''} placement="left">
              <Button
                size="small"
                className={`${index === drawerIndex && visible ? 'drawer-right-btn-active' : ''}`}
                onClick={() => {
                  setDrawerIndex(index);
                  setVisible(true);
                  changeIndex?.(index);
                  changeVisible?.(true);
                }}
                icon={item.icon}
                type={index === drawerIndex ? 'actiontext' : 'text'}
              ></Button>
            </Tooltip>
          ))}
        </Space>
      </div>
    </>
  );
};

export default FlexDrawerArr;
