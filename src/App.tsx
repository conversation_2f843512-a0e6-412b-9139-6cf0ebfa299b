import {Suspense, useEffect, useState} from 'react';
import {Modal} from 'acud';

import {Provider} from 'react-redux';
import store from '@store/index';

import {useDocumentTitle, useAppContext, AppContextActionType} from '@baidu/bce-react-toolkit';
import {Loading} from 'acud';
import Router from './router';
import {useRegion} from '@hooks/useRegion';
import {getGlobalPermission, queryIamStsRole, setGlobalRegion, checkWhiteList} from '@api/auth';
import {REGION_SETTINGS} from '@utils/utils';
import flags from './flags';

export default function App() {
  // 权限加载状态 - 确保权限加载完成后再渲染路由
  const [initLoading, setInitLoading] = useState(true);
  const {appState, appDispatch} = useAppContext();

  const onRegionChange = () => {
    document.querySelector('#bce-content .header .content .header-select .select-items')?.remove();
    window.location.reload();
  };
  useRegion({onRegionChange});

  async function init() {
    setInitLoading(true);

    try {
      // 非私有化模式， 请求白名单 &  请求开通逻辑
      if (!flags.DatabuilderPrivateSwitch) {
        // 白名单检查
        const whiteListCheck = await checkWhiteList();
        if (!whiteListCheck.success) {
          throw new Error('白名单获取失败');
        }

        if (!whiteListCheck.result.inWhitelist) {
          // 跳转到白名单申请页面
          window.location.href = 'https://cloud.baidu.com/survey/databuilderapply.html';
          return;
        }

        // 查询是否激活产品
        const iamRoleInfo = await queryIamStsRole();
        if (iamRoleInfo.success) {
          if (iamRoleInfo.result?.id) {
            appDispatch({
              type: AppContextActionType.ACTIVATE_PRODUCT
            });
          } else {
            // 未激活产品，不继续请求权限，直接进入激活页面
            setInitLoading(false);
            return;
          }
        } else {
          throw new Error('激活产品获取失败');
        }
      }

      const globalPermission = await getGlobalPermission();
      if (globalPermission.success) {
        store.dispatch({
          type: 'globalAuth/updateGlobalAuth',
          payload: globalPermission.result
        });
      } else {
        throw new Error('全局权限获取失败');
      }

      setInitLoading(false);
    } catch (error) {
      console.error(error);
      Modal.confirm({
        title: '提示',
        content: '初始化失败，请点击重试!',
        onOk: () => {
          init();
        }
      });
      setInitLoading(false);
    }
  }

  // 获取全局权限
  useEffect(() => {
    init();
    // 非私有化模式 处理region
    if (!flags.DatabuilderPrivateSwitch) {
      const currentRegion = window.$framework.region.getCurrentRegion()?.id || REGION_SETTINGS.DEFAULT_REGION;
      if (
        !REGION_SETTINGS.SUPPORT_REGION.includes(currentRegion) &&
        !window.location.host.includes('localhost')
      ) {
        setGlobalRegion({regionId: REGION_SETTINGS.DEFAULT_REGION}).then(() => {
          onRegionChange();
        });
      }
      // 处理region disable
      document
        .querySelectorAll('#bce-content .header .content .header-select .select-items li > .select-item')
        ?.forEach((item) => {
          const curItem = item as HTMLElement;
          if (
            curItem &&
            curItem.style &&
            !REGION_SETTINGS.SUPPORT_REGION.includes(curItem?.getAttribute('data-region') || '')
          ) {
            const parent = curItem.parentNode;
            const grandParent = parent?.parentNode;
            grandParent?.removeChild(parent);
          }
        });
    }
  }, []);

  // FIXME：会跟 privateStaticWorking 方法有重复设置 document.title，暂时先这样
  useDocumentTitle(flags.DatabuilderPrivateSwitch ? window?.PRIVATE_STATIC?.browserConfig?.title : '');

  return (
    <Provider store={store}>
      <Suspense fallback={<Loading loading />}>{initLoading ? <Loading loading /> : <Router />}</Suspense>
    </Provider>
  );
}
